# Generated by Cargo
# will have compiled files and executables
debug/
target/
target-*/

# These are backup files generated by rustfmt
**/*.rs.bk

# MSVC Windows builds of rustc generate these, which store debugging information
*.pdb

.vscode
/.idea

# perf & flamegraph
perf.data
perf.data.old
flamegraph.svg

root-target

nohup.out

.DS_Store

components.d.ts

musl_gcc

# log
easytier-panic.log

# web
node_modules

.vite

easytier-gui/src-tauri/*.dll
