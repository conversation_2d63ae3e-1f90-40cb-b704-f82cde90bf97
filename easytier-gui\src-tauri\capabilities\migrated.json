{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "migrated", "description": "permissions that were migrated from v1", "local": true, "windows": ["main"], "permissions": ["core:path:default", "core:event:default", "core:window:default", "core:window:allow-is-visible", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-set-focus", "core:window:allow-set-title", "core:app:default", "core:resources:default", "core:menu:default", "core:tray:default", "shell:allow-open", "process:allow-exit", "clipboard-manager:allow-read-text", "clipboard-manager:allow-write-text", "shell:default", "process:default", "clipboard-manager:default", "core:tray:allow-new", "core:tray:allow-set-menu", "core:tray:allow-set-title", "core:tray:allow-remove-by-id", "core:tray:allow-get-by-id", "core:tray:allow-set-icon", "core:tray:allow-set-icon-as-template", "core:tray:allow-set-show-menu-on-left-click", "core:tray:allow-set-tooltip", "vpnservice:allow-ping", "vpnservice:allow-prepare-vpn", "vpnservice:allow-start-vpn", "vpnservice:allow-stop-vpn", "vpnservice:allow-registerListener", "os:default", "os:allow-os-type", "os:allow-arch", "os:allow-hostname", "os:allow-platform", "os:allow-locale", "autostart:default", "autostart:allow-disable", "autostart:allow-enable", "autostart:allow-is-enabled"]}