<script setup lang="ts">
import { Card, useToast } from 'primevue';
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { Api, Utils } from 'easytier-frontend-lib';

const props = defineProps({
    api: Api.ApiClient,
});

const toast = useToast();

const summary = ref<Api.Summary | undefined>(undefined);

const loadSummary = async () => {
    const resp = await props.api?.get_summary();
    summary.value = resp;
};

const periodFunc = new Utils.PeriodicTask(async () => {
    try {
        await loadSummary();
    } catch (e) {
        toast.add({ severity: 'error', summary: 'Load Summary Failed', detail: e, life: 2000 });
        console.error(e);
    }
}, 1000);

onMounted(async () => {
    periodFunc.start();
});

onUnmounted(() => {
    periodFunc.stop();
});

const deviceCount = computed<number | undefined>(
    () => {
        return summary.value?.device_count;
    },
);

</script>

<template>
    <div class="grid grid-cols-3 gap-4">
        <Card class="h-full">
            <template #title>Device Count</template>
            <template #content>
                <div class="w-full flex justify-center text-7xl font-bold text-green-800 mt-4">
                    {{ deviceCount }}
                </div>
            </template>
        </Card>
        <div class="flex items-center justify-center rounded bg-gray-50 dark:bg-gray-800">
            <p class="text-2xl text-gray-400 dark:text-gray-500">
                <!-- <svg class="w-3.5 h-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 18 18">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 1v16M1 9h16" />
                </svg> -->
            </p>
        </div>
    </div>

</template>