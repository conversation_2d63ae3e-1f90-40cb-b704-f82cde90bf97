# 用于交叉编译的Dockerfile
FROM rust:latest

# 安装必要的依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    clang \
    libclang-dev \
    pkg-config \
    libc6-dev \
    gcc-multilib \
    protobuf-compiler \
    && rm -rf /var/lib/apt/lists/*

# 设置环境变量
ENV LIBCLANG_PATH=/usr/lib/llvm-14/lib

# 添加目标平台
RUN rustup target add x86_64-unknown-linux-gnu

# 设置工作目录
WORKDIR /opt

# 默认命令
CMD ["cargo", "build", "--release", "--target", "x86_64-unknown-linux-gnu"]
