//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(unique)]
    pub username: String,
    pub password: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::user_running_network_configs::Entity")]
    UserRunningNetworkConfigs,
    #[sea_orm(has_many = "super::users_groups::Entity")]
    UsersGroups,
}

impl Related<super::user_running_network_configs::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserRunningNetworkConfigs.def()
    }
}

impl Related<super::users_groups::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UsersGroups.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
