{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"~/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client", "vite-plugin-vue-layouts/client", "unplugin-vue-macros/macros-global", "unplugin-vue-router/client"], "allowImportingTsExtensions": true, "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "noEmit": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "vueCompilerOptions": {"plugins": ["@vue-macros/volar/define-models", "@vue-macros/volar/define-slots"]}, "exclude": ["dist", "node_modules"]}