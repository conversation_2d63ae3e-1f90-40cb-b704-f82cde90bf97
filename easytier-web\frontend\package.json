{"name": "easytier-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@primevue/themes": "4.3.3", "aura": "link:@primevue/themes/aura", "axios": "^1.7.7", "easytier-frontend-lib": "workspace:*", "primevue": "4.3.3", "tailwindcss-primeui": "^0.3.4", "vue": "^3.5.12", "vue-router": "4"}, "devDependencies": {"@types/node": "^22.8.6", "@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "=3.4.17", "typescript": "~5.6.2", "vite": "^5.4.10", "vite-plugin-singlefile": "^2.0.3", "vue-tsc": "^2.1.10"}}