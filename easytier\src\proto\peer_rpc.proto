syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "common.proto";

package peer_rpc;

message RoutePeerInfo {
  // means next hop in route table.
  uint32 peer_id = 1;
  common.UUID inst_id = 2;
  uint32 cost = 3;
  optional common.Ipv4Addr ipv4_addr = 4;
  repeated string proxy_cidrs = 5;
  optional string hostname = 6;
  common.NatType udp_stun_info = 7;
  google.protobuf.Timestamp last_update = 8;
  uint32 version = 9;

  string easytier_version = 10;
  common.PeerFeatureFlag feature_flag = 11;
  uint64 peer_route_id = 12;

  uint32 network_length = 13;
}

message PeerIdVersion {
  uint32 peer_id = 1;
  uint32 version = 2;
}

message RouteConnBitmap {
  repeated PeerIdVersion peer_ids = 1;
  bytes bitmap = 2;
}

message RoutePeerInfos { repeated RoutePeerInfo items = 1; }

message ForeignNetworkRouteInfoKey {
  uint32 peer_id = 1;
  string network_name = 2;
}

message ForeignNetworkRouteInfoEntry {
  repeated uint32 foreign_peer_ids = 1;
  google.protobuf.Timestamp last_update = 2;
  uint32 version = 3;
  bytes network_secret_digest = 4;
}

message RouteForeignNetworkInfos {
  message Info {
    ForeignNetworkRouteInfoKey key = 1;
    ForeignNetworkRouteInfoEntry value = 2;
  }
  repeated Info infos = 1;
}

message SyncRouteInfoRequest {
  uint32 my_peer_id = 1;
  uint64 my_session_id = 2;
  bool is_initiator = 3;
  RoutePeerInfos peer_infos = 4;
  RouteConnBitmap conn_bitmap = 5;
  RouteForeignNetworkInfos foreign_network_infos = 6;
}

enum SyncRouteInfoError {
  DuplicatePeerId = 0;
  Stopped = 1;
}

message SyncRouteInfoResponse {
  bool is_initiator = 1;
  uint64 session_id = 2;
  optional SyncRouteInfoError error = 3;
}

service OspfRouteRpc {
  // Generates a "hello" greeting based on the supplied info.
  rpc SyncRouteInfo(SyncRouteInfoRequest) returns (SyncRouteInfoResponse);
}

message GetIpListRequest {}

message GetIpListResponse {
  common.Ipv4Addr public_ipv4 = 1;
  repeated common.Ipv4Addr interface_ipv4s = 2;
  common.Ipv6Addr public_ipv6 = 3;
  repeated common.Ipv6Addr interface_ipv6s = 4;
  repeated common.Url listeners = 5;
}

service DirectConnectorRpc {
  rpc GetIpList(GetIpListRequest) returns (GetIpListResponse);
}

message SelectPunchListenerRequest {
  bool force_new = 1;
}

message SelectPunchListenerResponse {
  common.SocketAddr listener_mapped_addr = 1;
}

message SendPunchPacketConeRequest {
  common.SocketAddr listener_mapped_addr = 1;
  common.SocketAddr dest_addr = 2;
  uint32 transaction_id = 3;
  // send this many packets in a batch
  uint32 packet_count_per_batch = 4;
  // send total this batch count, total packet count = packet_batch_size * packet_batch_count
  uint32 packet_batch_count = 5;
  // interval between each batch
  uint32 packet_interval_ms = 6;
}

message SendPunchPacketHardSymRequest {
  common.SocketAddr listener_mapped_addr = 1;

  repeated common.Ipv4Addr public_ips = 2;
  uint32 transaction_id = 3;
  uint32 port_index = 4;
  uint32 round = 5;
}

message SendPunchPacketHardSymResponse { uint32 next_port_index = 1; }

message SendPunchPacketEasySymRequest {
  common.SocketAddr listener_mapped_addr = 1;
  repeated common.Ipv4Addr public_ips = 2;
  uint32 transaction_id = 3;

  uint32 base_port_num = 4;
  uint32 max_port_num = 5;
  bool is_incremental = 6;
}

message SendPunchPacketBothEasySymRequest {
  uint32 udp_socket_count = 1;
  common.Ipv4Addr public_ip = 2;
  uint32 transaction_id = 3;

  uint32 dst_port_num = 4;
  uint32 wait_time_ms = 5;
}

message SendPunchPacketBothEasySymResponse {
  // is doing punch with other peer
  bool is_busy = 1;
  common.SocketAddr base_mapped_addr = 2;
}

service UdpHolePunchRpc {
  rpc SelectPunchListener(SelectPunchListenerRequest)
      returns (SelectPunchListenerResponse);

  // send packet to one remote_addr, used by nat1-3 to nat1-3
  rpc SendPunchPacketCone(SendPunchPacketConeRequest) returns (common.Void);

  // send packet to multiple remote_addr (birthday attack), used by nat4 to nat1-3
  rpc SendPunchPacketHardSym(SendPunchPacketHardSymRequest)
      returns (SendPunchPacketHardSymResponse);
  rpc SendPunchPacketEasySym(SendPunchPacketEasySymRequest)
      returns (common.Void);

  // nat4 to nat4 (both predictably)
  rpc SendPunchPacketBothEasySym(SendPunchPacketBothEasySymRequest)
      returns (SendPunchPacketBothEasySymResponse);
}

message DirectConnectedPeerInfo { int32 latency_ms = 1; }

message PeerInfoForGlobalMap {
  map<uint32, DirectConnectedPeerInfo> direct_peers = 1;
}

message ReportPeersRequest {
  uint32 my_peer_id = 1;
  PeerInfoForGlobalMap peer_infos = 2;
}

message ReportPeersResponse {}

message GlobalPeerMap { map<uint32, PeerInfoForGlobalMap> map = 1; }

message GetGlobalPeerMapRequest { uint64 digest = 1; }

message GetGlobalPeerMapResponse {
  map<uint32, PeerInfoForGlobalMap> global_peer_map = 1;
  optional uint64 digest = 2;
}

service PeerCenterRpc {
  rpc ReportPeers(ReportPeersRequest) returns (ReportPeersResponse);
  rpc GetGlobalPeerMap(GetGlobalPeerMapRequest)
      returns (GetGlobalPeerMapResponse);
}

message HandshakeRequest {
  uint32 magic = 1;
  uint32 my_peer_id = 2;
  uint32 version = 3;
  repeated string features = 4;
  string network_name = 5;
  bytes network_secret_digrest = 6;
}

message KcpConnData {
  common.SocketAddr src = 1;
  common.SocketAddr dst = 4;
}

// Proxy Forward RPC Messages
message ProxyForwardRequest {
  uint32 session_id = 1;
  common.SocketAddr src_addr = 2;
  common.SocketAddr dst_addr = 3;
  string protocol = 4; // "tcp" or "udp"
}

message ProxyForwardResponse {
  uint32 session_id = 1;
  bool success = 2;
  optional string error_message = 3;
}

message ProxyForwardData {
  uint32 session_id = 1;
  bytes data = 2;
}

message ProxyForwardClose {
  uint32 session_id = 1;
  optional string reason = 2;
}

// Proxy Forward RPC Service
service ProxyForwardRpc {
  rpc CreateProxySession(ProxyForwardRequest) returns (ProxyForwardResponse);
  rpc SendData(ProxyForwardData) returns (common.Void);
  rpc CloseSession(ProxyForwardClose) returns (common.Void);
}
