name: EasyTier Docker

on:
  workflow_dispatch:
    inputs:
      run_id:
        description: 'The run id of EasyTier-Core Action in EasyTier repo'
        type: number
        default: 10228239965
        required: true
      image_tag:
        description: 'Tag for this image build'
        type: string
        default: 'v2.3.0'
        required: true
      mark_latest:
        description: 'Mark this image as latest'
        type: boolean
        default: false
        required: true

jobs:
  docker:
    if: contains('["KKRainbow"]', github.actor)
    runs-on: ubuntu-latest
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4
      -
        name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      -
        name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: login github container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Download artifact
        id: download-artifact
        uses: dawidd6/action-download-artifact@v6
        with:
          github_token: ${{secrets.GITHUB_TOKEN}}
          run_id: ${{ inputs.run_id }}
          repo: EasyTier/EasyTier
          path: docker_context
      - name: List files
        run: |
          ls -l -R .
      -
        name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: ./docker_context
          platforms: linux/amd64,linux/arm64
          push: true
          file: .github/workflows/Dockerfile
          tags: |
            easytier/easytier:${{ inputs.image_tag }}${{ inputs.mark_latest && ',easytier/easytier:latest' || '' }},
            ghcr.io/easytier/easytier:${{ inputs.image_tag }}${{ inputs.mark_latest && ',easytier/easytier:latest' || '' }},
