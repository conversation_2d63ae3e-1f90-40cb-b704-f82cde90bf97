{"$schema": "http://json-schema.org/draft-07/schema#", "title": "CapabilityFile", "description": "Capability formats accepted in a capability file.", "anyOf": [{"description": "A single capability.", "allOf": [{"$ref": "#/definitions/Capability"}]}, {"description": "A list of capabilities.", "type": "array", "items": {"$ref": "#/definitions/Capability"}}, {"description": "A list of capabilities.", "type": "object", "required": ["capabilities"], "properties": {"capabilities": {"description": "The list of capabilities.", "type": "array", "items": {"$ref": "#/definitions/Capability"}}}}], "definitions": {"Capability": {"description": "A grouping and boundary mechanism developers can use to isolate access to the IPC layer.\n\nIt controls application windows fine grained access to the Tauri core, application, or plugin commands. If a window is not matching any capability then it has no access to the IPC layer at all.\n\nThis can be done to create groups of windows, based on their required system access, which can reduce impact of frontend vulnerabilities in less privileged windows. Windows can be added to a capability by exact name (e.g. `main-window`) or glob patterns like `*` or `admin-*`. A Window can have none, one, or multiple associated capabilities.\n\n## Example\n\n```json { \"identifier\": \"main-user-files-write\", \"description\": \"This capability allows the `main` window on macOS and Windows access to `filesystem` write related commands and `dialog` commands to enable programatic access to files selected by the user.\", \"windows\": [ \"main\" ], \"permissions\": [ \"core:default\", \"dialog:open\", { \"identifier\": \"fs:allow-write-text-file\", \"allow\": [{ \"path\": \"$HOME/test.txt\" }] }, ], \"platforms\": [\"macOS\",\"windows\"] } ```", "type": "object", "required": ["identifier", "permissions"], "properties": {"identifier": {"description": "Identifier of the capability.\n\n## Example\n\n`main-user-files-write`", "type": "string"}, "description": {"description": "Description of what the capability is intended to allow on associated windows.\n\nIt should contain a description of what the grouped permissions should allow.\n\n## Example\n\nThis capability allows the `main` window access to `filesystem` write related commands and `dialog` commands to enable programatic access to files selected by the user.", "default": "", "type": "string"}, "remote": {"description": "Configure remote URLs that can use the capability permissions.\n\nThis setting is optional and defaults to not being set, as our default use case is that the content is served from our local application.\n\n:::caution Make sure you understand the security implications of providing remote sources with local system access. :::\n\n## Example\n\n```json { \"urls\": [\"https://*.mydomain.dev\"] } ```", "anyOf": [{"$ref": "#/definitions/CapabilityRemote"}, {"type": "null"}]}, "local": {"description": "Whether this capability is enabled for local app URLs or not. Defaults to `true`.", "default": true, "type": "boolean"}, "windows": {"description": "List of windows that are affected by this capability. Can be a glob pattern.\n\nOn multiwebview windows, prefer [`Self::webviews`] for a fine grained access control.\n\n## Example\n\n`[\"main\"]`", "type": "array", "items": {"type": "string"}}, "webviews": {"description": "List of webviews that are affected by this capability. Can be a glob pattern.\n\nThis is only required when using on multiwebview contexts, by default all child webviews of a window that matches [`Self::windows`] are linked.\n\n## Example\n\n`[\"sub-webview-one\", \"sub-webview-two\"]`", "type": "array", "items": {"type": "string"}}, "permissions": {"description": "List of permissions attached to this capability.\n\nMust include the plugin name as prefix in the form of `${plugin-name}:${permission-name}`. For commands directly implemented in the application itself only `${permission-name}` is required.\n\n## Example\n\n```json [ \"core:default\", \"shell:allow-open\", \"dialog:open\", { \"identifier\": \"fs:allow-write-text-file\", \"allow\": [{ \"path\": \"$HOME/test.txt\" }] } ] ```", "type": "array", "items": {"$ref": "#/definitions/PermissionEntry"}, "uniqueItems": true}, "platforms": {"description": "Limit which target platforms this capability applies to.\n\nBy default all platforms are targeted.\n\n## Example\n\n`[\"macOS\",\"windows\"]`", "type": ["array", "null"], "items": {"$ref": "#/definitions/Target"}}}}, "CapabilityRemote": {"description": "Configuration for remote URLs that are associated with the capability.", "type": "object", "required": ["urls"], "properties": {"urls": {"description": "Remote domains this capability refers to using the [URLPattern standard](https://urlpattern.spec.whatwg.org/).\n\n## Examples\n\n- \"https://*.mydomain.dev\": allows subdomains of mydomain.dev - \"https://mydomain.dev/api/*\": allows any subpath of mydomain.dev/api", "type": "array", "items": {"type": "string"}}}}, "PermissionEntry": {"description": "An entry for a permission value in a [`Capability`] can be either a raw permission [`Identifier`] or an object that references a permission and extends its scope.", "anyOf": [{"description": "Reference a permission or permission set by identifier.", "allOf": [{"$ref": "#/definitions/Identifier"}]}, {"description": "Reference a permission or permission set by identifier and extends its scope.", "type": "object", "allOf": [{"if": {"properties": {"identifier": {"anyOf": [{"description": "This permission set configures which\nshell functionality is exposed by default.\n\n#### Granted Permissions\n\nIt allows to use the `open` functionality without any specific\nscope pre-configured. It will allow opening `http(s)://`,\n`tel:` and `mailto:` links.\n", "type": "string", "const": "shell:default"}, {"description": "Enables the execute command without any pre-configured scope.", "type": "string", "const": "shell:allow-execute"}, {"description": "Enables the kill command without any pre-configured scope.", "type": "string", "const": "shell:allow-kill"}, {"description": "Enables the open command without any pre-configured scope.", "type": "string", "const": "shell:allow-open"}, {"description": "Enables the spawn command without any pre-configured scope.", "type": "string", "const": "shell:allow-spawn"}, {"description": "Enables the stdin_write command without any pre-configured scope.", "type": "string", "const": "shell:allow-stdin-write"}, {"description": "Denies the execute command without any pre-configured scope.", "type": "string", "const": "shell:deny-execute"}, {"description": "Denies the kill command without any pre-configured scope.", "type": "string", "const": "shell:deny-kill"}, {"description": "Denies the open command without any pre-configured scope.", "type": "string", "const": "shell:deny-open"}, {"description": "Denies the spawn command without any pre-configured scope.", "type": "string", "const": "shell:deny-spawn"}, {"description": "Denies the stdin_write command without any pre-configured scope.", "type": "string", "const": "shell:deny-stdin-write"}]}}}, "then": {"properties": {"allow": {"items": {"title": "ShellScopeEntry", "description": "Shell scope entry.", "anyOf": [{"type": "object", "required": ["cmd", "name"], "properties": {"args": {"description": "The allowed arguments for the command execution.", "allOf": [{"$ref": "#/definitions/ShellScopeEntryAllowedArgs"}]}, "cmd": {"description": "The command name. It can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}, "name": {"description": "The name for this allowed shell command configuration.\n\nThis name will be used inside of the webview API to call this command along with any specified arguments.", "type": "string"}}, "additionalProperties": false}, {"type": "object", "required": ["name", "sidecar"], "properties": {"args": {"description": "The allowed arguments for the command execution.", "allOf": [{"$ref": "#/definitions/ShellScopeEntryAllowedArgs"}]}, "name": {"description": "The name for this allowed shell command configuration.\n\nThis name will be used inside of the webview API to call this command along with any specified arguments.", "type": "string"}, "sidecar": {"description": "If this command is a sidecar command.", "type": "boolean"}}, "additionalProperties": false}]}}, "deny": {"items": {"title": "ShellScopeEntry", "description": "Shell scope entry.", "anyOf": [{"type": "object", "required": ["cmd", "name"], "properties": {"args": {"description": "The allowed arguments for the command execution.", "allOf": [{"$ref": "#/definitions/ShellScopeEntryAllowedArgs"}]}, "cmd": {"description": "The command name. It can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}, "name": {"description": "The name for this allowed shell command configuration.\n\nThis name will be used inside of the webview API to call this command along with any specified arguments.", "type": "string"}}, "additionalProperties": false}, {"type": "object", "required": ["name", "sidecar"], "properties": {"args": {"description": "The allowed arguments for the command execution.", "allOf": [{"$ref": "#/definitions/ShellScopeEntryAllowedArgs"}]}, "name": {"description": "The name for this allowed shell command configuration.\n\nThis name will be used inside of the webview API to call this command along with any specified arguments.", "type": "string"}, "sidecar": {"description": "If this command is a sidecar command.", "type": "boolean"}}, "additionalProperties": false}]}}}}, "properties": {"identifier": {"description": "Identifier of the permission or permission set.", "allOf": [{"$ref": "#/definitions/Identifier"}]}}}, {"properties": {"identifier": {"description": "Identifier of the permission or permission set.", "allOf": [{"$ref": "#/definitions/Identifier"}]}, "allow": {"description": "Data that defines what is allowed by the scope.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}, "deny": {"description": "Data that defines what is denied by the scope. This should be prioritized by validation logic.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}}}], "required": ["identifier"]}]}, "Identifier": {"description": "Permission identifier", "oneOf": [{"description": "This permission set configures if your\napplication can enable or disable auto\nstarting the application on boot.\n\n#### Granted Permissions\n\nIt allows all to check, enable and\ndisable the automatic start on boot.\n\n", "type": "string", "const": "autostart:default"}, {"description": "Enables the disable command without any pre-configured scope.", "type": "string", "const": "autostart:allow-disable"}, {"description": "Enables the enable command without any pre-configured scope.", "type": "string", "const": "autostart:allow-enable"}, {"description": "Enables the is_enabled command without any pre-configured scope.", "type": "string", "const": "autostart:allow-is-enabled"}, {"description": "Denies the disable command without any pre-configured scope.", "type": "string", "const": "autostart:deny-disable"}, {"description": "Denies the enable command without any pre-configured scope.", "type": "string", "const": "autostart:deny-enable"}, {"description": "Denies the is_enabled command without any pre-configured scope.", "type": "string", "const": "autostart:deny-is-enabled"}, {"description": "No features are enabled by default, as we believe\nthe clipboard can be inherently dangerous and it is \napplication specific if read and/or write access is needed.\n\nClipboard interaction needs to be explicitly enabled.\n", "type": "string", "const": "clipboard-manager:default"}, {"description": "Enables the clear command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:allow-clear"}, {"description": "Enables the read_image command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:allow-read-image"}, {"description": "Enables the read_text command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:allow-read-text"}, {"description": "Enables the write_html command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:allow-write-html"}, {"description": "Enables the write_image command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:allow-write-image"}, {"description": "Enables the write_text command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:allow-write-text"}, {"description": "Denies the clear command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:deny-clear"}, {"description": "Denies the read_image command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:deny-read-image"}, {"description": "Denies the read_text command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:deny-read-text"}, {"description": "Denies the write_html command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:deny-write-html"}, {"description": "Denies the write_image command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:deny-write-image"}, {"description": "Denies the write_text command without any pre-configured scope.", "type": "string", "const": "clipboard-manager:deny-write-text"}, {"description": "Default core plugins set which includes:\n- 'core:path:default'\n- 'core:event:default'\n- 'core:window:default'\n- 'core:webview:default'\n- 'core:app:default'\n- 'core:image:default'\n- 'core:resources:default'\n- 'core:menu:default'\n- 'core:tray:default'\n", "type": "string", "const": "core:default"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:app:default"}, {"description": "Enables the app_hide command without any pre-configured scope.", "type": "string", "const": "core:app:allow-app-hide"}, {"description": "Enables the app_show command without any pre-configured scope.", "type": "string", "const": "core:app:allow-app-show"}, {"description": "Enables the default_window_icon command without any pre-configured scope.", "type": "string", "const": "core:app:allow-default-window-icon"}, {"description": "Enables the name command without any pre-configured scope.", "type": "string", "const": "core:app:allow-name"}, {"description": "Enables the set_app_theme command without any pre-configured scope.", "type": "string", "const": "core:app:allow-set-app-theme"}, {"description": "Enables the tauri_version command without any pre-configured scope.", "type": "string", "const": "core:app:allow-tauri-version"}, {"description": "Enables the version command without any pre-configured scope.", "type": "string", "const": "core:app:allow-version"}, {"description": "Denies the app_hide command without any pre-configured scope.", "type": "string", "const": "core:app:deny-app-hide"}, {"description": "Denies the app_show command without any pre-configured scope.", "type": "string", "const": "core:app:deny-app-show"}, {"description": "Denies the default_window_icon command without any pre-configured scope.", "type": "string", "const": "core:app:deny-default-window-icon"}, {"description": "Denies the name command without any pre-configured scope.", "type": "string", "const": "core:app:deny-name"}, {"description": "Denies the set_app_theme command without any pre-configured scope.", "type": "string", "const": "core:app:deny-set-app-theme"}, {"description": "Denies the tauri_version command without any pre-configured scope.", "type": "string", "const": "core:app:deny-tauri-version"}, {"description": "Denies the version command without any pre-configured scope.", "type": "string", "const": "core:app:deny-version"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:event:default"}, {"description": "Enables the emit command without any pre-configured scope.", "type": "string", "const": "core:event:allow-emit"}, {"description": "Enables the emit_to command without any pre-configured scope.", "type": "string", "const": "core:event:allow-emit-to"}, {"description": "Enables the listen command without any pre-configured scope.", "type": "string", "const": "core:event:allow-listen"}, {"description": "Enables the unlisten command without any pre-configured scope.", "type": "string", "const": "core:event:allow-unlisten"}, {"description": "Denies the emit command without any pre-configured scope.", "type": "string", "const": "core:event:deny-emit"}, {"description": "Denies the emit_to command without any pre-configured scope.", "type": "string", "const": "core:event:deny-emit-to"}, {"description": "Denies the listen command without any pre-configured scope.", "type": "string", "const": "core:event:deny-listen"}, {"description": "Denies the unlisten command without any pre-configured scope.", "type": "string", "const": "core:event:deny-unlisten"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:image:default"}, {"description": "Enables the from_bytes command without any pre-configured scope.", "type": "string", "const": "core:image:allow-from-bytes"}, {"description": "Enables the from_path command without any pre-configured scope.", "type": "string", "const": "core:image:allow-from-path"}, {"description": "Enables the new command without any pre-configured scope.", "type": "string", "const": "core:image:allow-new"}, {"description": "Enables the rgba command without any pre-configured scope.", "type": "string", "const": "core:image:allow-rgba"}, {"description": "Enables the size command without any pre-configured scope.", "type": "string", "const": "core:image:allow-size"}, {"description": "Denies the from_bytes command without any pre-configured scope.", "type": "string", "const": "core:image:deny-from-bytes"}, {"description": "Denies the from_path command without any pre-configured scope.", "type": "string", "const": "core:image:deny-from-path"}, {"description": "Denies the new command without any pre-configured scope.", "type": "string", "const": "core:image:deny-new"}, {"description": "Denies the rgba command without any pre-configured scope.", "type": "string", "const": "core:image:deny-rgba"}, {"description": "Denies the size command without any pre-configured scope.", "type": "string", "const": "core:image:deny-size"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:menu:default"}, {"description": "Enables the append command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-append"}, {"description": "Enables the create_default command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-create-default"}, {"description": "Enables the get command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-get"}, {"description": "Enables the insert command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-insert"}, {"description": "Enables the is_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-is-checked"}, {"description": "Enables the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-is-enabled"}, {"description": "Enables the items command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-items"}, {"description": "Enables the new command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-new"}, {"description": "Enables the popup command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-popup"}, {"description": "Enables the prepend command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-prepend"}, {"description": "Enables the remove command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-remove"}, {"description": "Enables the remove_at command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-remove-at"}, {"description": "Enables the set_accelerator command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-accelerator"}, {"description": "Enables the set_as_app_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-app-menu"}, {"description": "Enables the set_as_help_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-help-menu-for-nsapp"}, {"description": "Enables the set_as_window_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-window-menu"}, {"description": "Enables the set_as_windows_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-windows-menu-for-nsapp"}, {"description": "Enables the set_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-checked"}, {"description": "Enables the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-enabled"}, {"description": "Enables the set_icon command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-icon"}, {"description": "Enables the set_text command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-text"}, {"description": "Enables the text command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-text"}, {"description": "Denies the append command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-append"}, {"description": "Denies the create_default command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-create-default"}, {"description": "Denies the get command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-get"}, {"description": "Denies the insert command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-insert"}, {"description": "Denies the is_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-is-checked"}, {"description": "Denies the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-is-enabled"}, {"description": "Denies the items command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-items"}, {"description": "Denies the new command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-new"}, {"description": "Denies the popup command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-popup"}, {"description": "Denies the prepend command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-prepend"}, {"description": "Denies the remove command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-remove"}, {"description": "Denies the remove_at command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-remove-at"}, {"description": "Denies the set_accelerator command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-accelerator"}, {"description": "Denies the set_as_app_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-app-menu"}, {"description": "Denies the set_as_help_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-help-menu-for-nsapp"}, {"description": "Denies the set_as_window_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-window-menu"}, {"description": "Denies the set_as_windows_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-windows-menu-for-nsapp"}, {"description": "Denies the set_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-checked"}, {"description": "Denies the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-enabled"}, {"description": "Denies the set_icon command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-icon"}, {"description": "Denies the set_text command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-text"}, {"description": "Denies the text command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-text"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:path:default"}, {"description": "Enables the basename command without any pre-configured scope.", "type": "string", "const": "core:path:allow-basename"}, {"description": "Enables the dirname command without any pre-configured scope.", "type": "string", "const": "core:path:allow-dirname"}, {"description": "Enables the extname command without any pre-configured scope.", "type": "string", "const": "core:path:allow-extname"}, {"description": "Enables the is_absolute command without any pre-configured scope.", "type": "string", "const": "core:path:allow-is-absolute"}, {"description": "Enables the join command without any pre-configured scope.", "type": "string", "const": "core:path:allow-join"}, {"description": "Enables the normalize command without any pre-configured scope.", "type": "string", "const": "core:path:allow-normalize"}, {"description": "Enables the resolve command without any pre-configured scope.", "type": "string", "const": "core:path:allow-resolve"}, {"description": "Enables the resolve_directory command without any pre-configured scope.", "type": "string", "const": "core:path:allow-resolve-directory"}, {"description": "Denies the basename command without any pre-configured scope.", "type": "string", "const": "core:path:deny-basename"}, {"description": "Denies the dirname command without any pre-configured scope.", "type": "string", "const": "core:path:deny-dirname"}, {"description": "Denies the extname command without any pre-configured scope.", "type": "string", "const": "core:path:deny-extname"}, {"description": "Denies the is_absolute command without any pre-configured scope.", "type": "string", "const": "core:path:deny-is-absolute"}, {"description": "Denies the join command without any pre-configured scope.", "type": "string", "const": "core:path:deny-join"}, {"description": "Denies the normalize command without any pre-configured scope.", "type": "string", "const": "core:path:deny-normalize"}, {"description": "Denies the resolve command without any pre-configured scope.", "type": "string", "const": "core:path:deny-resolve"}, {"description": "Denies the resolve_directory command without any pre-configured scope.", "type": "string", "const": "core:path:deny-resolve-directory"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:resources:default"}, {"description": "Enables the close command without any pre-configured scope.", "type": "string", "const": "core:resources:allow-close"}, {"description": "Denies the close command without any pre-configured scope.", "type": "string", "const": "core:resources:deny-close"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:tray:default"}, {"description": "Enables the get_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-get-by-id"}, {"description": "Enables the new command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-new"}, {"description": "Enables the remove_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-remove-by-id"}, {"description": "Enables the set_icon command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-icon"}, {"description": "Enables the set_icon_as_template command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-icon-as-template"}, {"description": "Enables the set_menu command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-menu"}, {"description": "Enables the set_show_menu_on_left_click command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-show-menu-on-left-click"}, {"description": "Enables the set_temp_dir_path command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-temp-dir-path"}, {"description": "Enables the set_title command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-title"}, {"description": "Enables the set_tooltip command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-tooltip"}, {"description": "Enables the set_visible command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-visible"}, {"description": "Denies the get_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-get-by-id"}, {"description": "Denies the new command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-new"}, {"description": "Denies the remove_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-remove-by-id"}, {"description": "Denies the set_icon command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-icon"}, {"description": "Denies the set_icon_as_template command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-icon-as-template"}, {"description": "Denies the set_menu command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-menu"}, {"description": "Denies the set_show_menu_on_left_click command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-show-menu-on-left-click"}, {"description": "Denies the set_temp_dir_path command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-temp-dir-path"}, {"description": "Denies the set_title command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-title"}, {"description": "Denies the set_tooltip command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-tooltip"}, {"description": "Denies the set_visible command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-visible"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:webview:default"}, {"description": "Enables the clear_all_browsing_data command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-clear-all-browsing-data"}, {"description": "Enables the create_webview command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-create-webview"}, {"description": "Enables the create_webview_window command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-create-webview-window"}, {"description": "Enables the get_all_webviews command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-get-all-webviews"}, {"description": "Enables the internal_toggle_devtools command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-internal-toggle-devtools"}, {"description": "Enables the print command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-print"}, {"description": "Enables the reparent command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-reparent"}, {"description": "Enables the set_webview_focus command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-focus"}, {"description": "Enables the set_webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-position"}, {"description": "Enables the set_webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-size"}, {"description": "Enables the set_webview_zoom command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-zoom"}, {"description": "Enables the webview_close command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-close"}, {"description": "Enables the webview_hide command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-hide"}, {"description": "Enables the webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-position"}, {"description": "Enables the webview_show command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-show"}, {"description": "Enables the webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-size"}, {"description": "Denies the clear_all_browsing_data command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-clear-all-browsing-data"}, {"description": "Denies the create_webview command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-create-webview"}, {"description": "Denies the create_webview_window command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-create-webview-window"}, {"description": "Denies the get_all_webviews command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-get-all-webviews"}, {"description": "Denies the internal_toggle_devtools command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-internal-toggle-devtools"}, {"description": "Denies the print command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-print"}, {"description": "Denies the reparent command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-reparent"}, {"description": "Denies the set_webview_focus command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-focus"}, {"description": "Denies the set_webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-position"}, {"description": "Denies the set_webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-size"}, {"description": "Denies the set_webview_zoom command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-zoom"}, {"description": "Denies the webview_close command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-close"}, {"description": "Denies the webview_hide command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-hide"}, {"description": "Denies the webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-position"}, {"description": "Denies the webview_show command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-show"}, {"description": "Denies the webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-size"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:window:default"}, {"description": "Enables the available_monitors command without any pre-configured scope.", "type": "string", "const": "core:window:allow-available-monitors"}, {"description": "Enables the center command without any pre-configured scope.", "type": "string", "const": "core:window:allow-center"}, {"description": "Enables the close command without any pre-configured scope.", "type": "string", "const": "core:window:allow-close"}, {"description": "Enables the create command without any pre-configured scope.", "type": "string", "const": "core:window:allow-create"}, {"description": "Enables the current_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:allow-current-monitor"}, {"description": "Enables the cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-cursor-position"}, {"description": "Enables the destroy command without any pre-configured scope.", "type": "string", "const": "core:window:allow-destroy"}, {"description": "Enables the get_all_windows command without any pre-configured scope.", "type": "string", "const": "core:window:allow-get-all-windows"}, {"description": "Enables the hide command without any pre-configured scope.", "type": "string", "const": "core:window:allow-hide"}, {"description": "Enables the inner_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-inner-position"}, {"description": "Enables the inner_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-inner-size"}, {"description": "Enables the internal_toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-internal-toggle-maximize"}, {"description": "Enables the is_closable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-closable"}, {"description": "Enables the is_decorated command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-decorated"}, {"description": "Enables the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-enabled"}, {"description": "Enables the is_focused command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-focused"}, {"description": "Enables the is_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-fullscreen"}, {"description": "Enables the is_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-maximizable"}, {"description": "Enables the is_maximized command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-maximized"}, {"description": "Enables the is_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-minimizable"}, {"description": "Enables the is_minimized command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-minimized"}, {"description": "Enables the is_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-resizable"}, {"description": "Enables the is_visible command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-visible"}, {"description": "Enables the maximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-maximize"}, {"description": "Enables the minimize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-minimize"}, {"description": "Enables the monitor_from_point command without any pre-configured scope.", "type": "string", "const": "core:window:allow-monitor-from-point"}, {"description": "Enables the outer_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-outer-position"}, {"description": "Enables the outer_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-outer-size"}, {"description": "Enables the primary_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:allow-primary-monitor"}, {"description": "Enables the request_user_attention command without any pre-configured scope.", "type": "string", "const": "core:window:allow-request-user-attention"}, {"description": "Enables the scale_factor command without any pre-configured scope.", "type": "string", "const": "core:window:allow-scale-factor"}, {"description": "Enables the set_always_on_bottom command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-always-on-bottom"}, {"description": "Enables the set_always_on_top command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-always-on-top"}, {"description": "Enables the set_closable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-closable"}, {"description": "Enables the set_content_protected command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-content-protected"}, {"description": "Enables the set_cursor_grab command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-grab"}, {"description": "Enables the set_cursor_icon command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-icon"}, {"description": "Enables the set_cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-position"}, {"description": "Enables the set_cursor_visible command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-visible"}, {"description": "Enables the set_decorations command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-decorations"}, {"description": "Enables the set_effects command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-effects"}, {"description": "Enables the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-enabled"}, {"description": "Enables the set_focus command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-focus"}, {"description": "Enables the set_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-fullscreen"}, {"description": "Enables the set_icon command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-icon"}, {"description": "Enables the set_ignore_cursor_events command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-ignore-cursor-events"}, {"description": "Enables the set_max_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-max-size"}, {"description": "Enables the set_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-maximizable"}, {"description": "Enables the set_min_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-min-size"}, {"description": "Enables the set_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-minimizable"}, {"description": "Enables the set_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-position"}, {"description": "Enables the set_progress_bar command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-progress-bar"}, {"description": "Enables the set_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-resizable"}, {"description": "Enables the set_shadow command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-shadow"}, {"description": "Enables the set_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-size"}, {"description": "Enables the set_size_constraints command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-size-constraints"}, {"description": "Enables the set_skip_taskbar command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-skip-taskbar"}, {"description": "Enables the set_theme command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-theme"}, {"description": "Enables the set_title command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-title"}, {"description": "Enables the set_title_bar_style command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-title-bar-style"}, {"description": "Enables the set_visible_on_all_workspaces command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-visible-on-all-workspaces"}, {"description": "Enables the show command without any pre-configured scope.", "type": "string", "const": "core:window:allow-show"}, {"description": "Enables the start_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:allow-start-dragging"}, {"description": "Enables the start_resize_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:allow-start-resize-dragging"}, {"description": "Enables the theme command without any pre-configured scope.", "type": "string", "const": "core:window:allow-theme"}, {"description": "Enables the title command without any pre-configured scope.", "type": "string", "const": "core:window:allow-title"}, {"description": "Enables the toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-toggle-maximize"}, {"description": "Enables the unmaximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-unmaximize"}, {"description": "Enables the unminimize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-unminimize"}, {"description": "Denies the available_monitors command without any pre-configured scope.", "type": "string", "const": "core:window:deny-available-monitors"}, {"description": "Denies the center command without any pre-configured scope.", "type": "string", "const": "core:window:deny-center"}, {"description": "Denies the close command without any pre-configured scope.", "type": "string", "const": "core:window:deny-close"}, {"description": "Denies the create command without any pre-configured scope.", "type": "string", "const": "core:window:deny-create"}, {"description": "Denies the current_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:deny-current-monitor"}, {"description": "Denies the cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-cursor-position"}, {"description": "Denies the destroy command without any pre-configured scope.", "type": "string", "const": "core:window:deny-destroy"}, {"description": "Denies the get_all_windows command without any pre-configured scope.", "type": "string", "const": "core:window:deny-get-all-windows"}, {"description": "Denies the hide command without any pre-configured scope.", "type": "string", "const": "core:window:deny-hide"}, {"description": "Denies the inner_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-inner-position"}, {"description": "Denies the inner_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-inner-size"}, {"description": "Denies the internal_toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-internal-toggle-maximize"}, {"description": "Denies the is_closable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-closable"}, {"description": "Denies the is_decorated command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-decorated"}, {"description": "Denies the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-enabled"}, {"description": "Denies the is_focused command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-focused"}, {"description": "Denies the is_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-fullscreen"}, {"description": "Denies the is_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-maximizable"}, {"description": "Denies the is_maximized command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-maximized"}, {"description": "Denies the is_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-minimizable"}, {"description": "Denies the is_minimized command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-minimized"}, {"description": "Denies the is_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-resizable"}, {"description": "Denies the is_visible command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-visible"}, {"description": "Denies the maximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-maximize"}, {"description": "Denies the minimize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-minimize"}, {"description": "Denies the monitor_from_point command without any pre-configured scope.", "type": "string", "const": "core:window:deny-monitor-from-point"}, {"description": "Denies the outer_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-outer-position"}, {"description": "Denies the outer_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-outer-size"}, {"description": "Denies the primary_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:deny-primary-monitor"}, {"description": "Denies the request_user_attention command without any pre-configured scope.", "type": "string", "const": "core:window:deny-request-user-attention"}, {"description": "Denies the scale_factor command without any pre-configured scope.", "type": "string", "const": "core:window:deny-scale-factor"}, {"description": "Denies the set_always_on_bottom command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-always-on-bottom"}, {"description": "Denies the set_always_on_top command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-always-on-top"}, {"description": "Denies the set_closable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-closable"}, {"description": "Denies the set_content_protected command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-content-protected"}, {"description": "Denies the set_cursor_grab command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-grab"}, {"description": "Denies the set_cursor_icon command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-icon"}, {"description": "Denies the set_cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-position"}, {"description": "Denies the set_cursor_visible command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-visible"}, {"description": "Denies the set_decorations command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-decorations"}, {"description": "Denies the set_effects command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-effects"}, {"description": "Denies the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-enabled"}, {"description": "Denies the set_focus command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-focus"}, {"description": "Denies the set_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-fullscreen"}, {"description": "Denies the set_icon command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-icon"}, {"description": "Denies the set_ignore_cursor_events command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-ignore-cursor-events"}, {"description": "Denies the set_max_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-max-size"}, {"description": "Denies the set_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-maximizable"}, {"description": "Denies the set_min_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-min-size"}, {"description": "Denies the set_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-minimizable"}, {"description": "Denies the set_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-position"}, {"description": "Denies the set_progress_bar command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-progress-bar"}, {"description": "Denies the set_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-resizable"}, {"description": "Denies the set_shadow command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-shadow"}, {"description": "Denies the set_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-size"}, {"description": "Denies the set_size_constraints command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-size-constraints"}, {"description": "Denies the set_skip_taskbar command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-skip-taskbar"}, {"description": "Denies the set_theme command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-theme"}, {"description": "Denies the set_title command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-title"}, {"description": "Denies the set_title_bar_style command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-title-bar-style"}, {"description": "Denies the set_visible_on_all_workspaces command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-visible-on-all-workspaces"}, {"description": "Denies the show command without any pre-configured scope.", "type": "string", "const": "core:window:deny-show"}, {"description": "Denies the start_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:deny-start-dragging"}, {"description": "Denies the start_resize_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:deny-start-resize-dragging"}, {"description": "Denies the theme command without any pre-configured scope.", "type": "string", "const": "core:window:deny-theme"}, {"description": "Denies the title command without any pre-configured scope.", "type": "string", "const": "core:window:deny-title"}, {"description": "Denies the toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-toggle-maximize"}, {"description": "Denies the unmaximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-unmaximize"}, {"description": "Denies the unminimize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-unminimize"}, {"description": "This permission set configures which\noperating system information are available\nto gather from the frontend.\n\n#### Granted Permissions\n\nAll information except the host name are available.\n\n", "type": "string", "const": "os:default"}, {"description": "Enables the arch command without any pre-configured scope.", "type": "string", "const": "os:allow-arch"}, {"description": "Enables the exe_extension command without any pre-configured scope.", "type": "string", "const": "os:allow-exe-extension"}, {"description": "Enables the family command without any pre-configured scope.", "type": "string", "const": "os:allow-family"}, {"description": "Enables the hostname command without any pre-configured scope.", "type": "string", "const": "os:allow-hostname"}, {"description": "Enables the locale command without any pre-configured scope.", "type": "string", "const": "os:allow-locale"}, {"description": "Enables the os_type command without any pre-configured scope.", "type": "string", "const": "os:allow-os-type"}, {"description": "Enables the platform command without any pre-configured scope.", "type": "string", "const": "os:allow-platform"}, {"description": "Enables the version command without any pre-configured scope.", "type": "string", "const": "os:allow-version"}, {"description": "Denies the arch command without any pre-configured scope.", "type": "string", "const": "os:deny-arch"}, {"description": "Denies the exe_extension command without any pre-configured scope.", "type": "string", "const": "os:deny-exe-extension"}, {"description": "Denies the family command without any pre-configured scope.", "type": "string", "const": "os:deny-family"}, {"description": "Denies the hostname command without any pre-configured scope.", "type": "string", "const": "os:deny-hostname"}, {"description": "Denies the locale command without any pre-configured scope.", "type": "string", "const": "os:deny-locale"}, {"description": "Denies the os_type command without any pre-configured scope.", "type": "string", "const": "os:deny-os-type"}, {"description": "Denies the platform command without any pre-configured scope.", "type": "string", "const": "os:deny-platform"}, {"description": "Denies the version command without any pre-configured scope.", "type": "string", "const": "os:deny-version"}, {"description": "Allows the moveWindow and handleIconState APIs", "type": "string", "const": "positioner:default"}, {"description": "Enables the move_window command without any pre-configured scope.", "type": "string", "const": "positioner:allow-move-window"}, {"description": "Enables the set_tray_icon_state command without any pre-configured scope.", "type": "string", "const": "positioner:allow-set-tray-icon-state"}, {"description": "Denies the move_window command without any pre-configured scope.", "type": "string", "const": "positioner:deny-move-window"}, {"description": "Denies the set_tray_icon_state command without any pre-configured scope.", "type": "string", "const": "positioner:deny-set-tray-icon-state"}, {"description": "This permission set configures which\nprocess feeatures are by default exposed.\n\n#### Granted Permissions\n\nThis enables to quit via `allow-exit` and restart via `allow-restart`\nthe application.\n", "type": "string", "const": "process:default"}, {"description": "Enables the exit command without any pre-configured scope.", "type": "string", "const": "process:allow-exit"}, {"description": "Enables the restart command without any pre-configured scope.", "type": "string", "const": "process:allow-restart"}, {"description": "Denies the exit command without any pre-configured scope.", "type": "string", "const": "process:deny-exit"}, {"description": "Denies the restart command without any pre-configured scope.", "type": "string", "const": "process:deny-restart"}, {"description": "This permission set configures which\nshell functionality is exposed by default.\n\n#### Granted Permissions\n\nIt allows to use the `open` functionality without any specific\nscope pre-configured. It will allow opening `http(s)://`,\n`tel:` and `mailto:` links.\n", "type": "string", "const": "shell:default"}, {"description": "Enables the execute command without any pre-configured scope.", "type": "string", "const": "shell:allow-execute"}, {"description": "Enables the kill command without any pre-configured scope.", "type": "string", "const": "shell:allow-kill"}, {"description": "Enables the open command without any pre-configured scope.", "type": "string", "const": "shell:allow-open"}, {"description": "Enables the spawn command without any pre-configured scope.", "type": "string", "const": "shell:allow-spawn"}, {"description": "Enables the stdin_write command without any pre-configured scope.", "type": "string", "const": "shell:allow-stdin-write"}, {"description": "Denies the execute command without any pre-configured scope.", "type": "string", "const": "shell:deny-execute"}, {"description": "Denies the kill command without any pre-configured scope.", "type": "string", "const": "shell:deny-kill"}, {"description": "Denies the open command without any pre-configured scope.", "type": "string", "const": "shell:deny-open"}, {"description": "Denies the spawn command without any pre-configured scope.", "type": "string", "const": "shell:deny-spawn"}, {"description": "Denies the stdin_write command without any pre-configured scope.", "type": "string", "const": "shell:deny-stdin-write"}, {"description": "Default permissions for the plugin", "type": "string", "const": "vpnservice:default"}, {"description": "Enables the ping command without any pre-configured scope.", "type": "string", "const": "vpnservice:allow-ping"}, {"description": "Enables the prepare_vpn command without any pre-configured scope.", "type": "string", "const": "vpnservice:allow-prepare-vpn"}, {"description": "Enables the register_listener command without any pre-configured scope.", "type": "string", "const": "vpnservice:allow-register-listener"}, {"description": "Enables the registerListener command without any pre-configured scope.", "type": "string", "const": "vpnservice:allow-registerListener"}, {"description": "Enables the start_vpn command without any pre-configured scope.", "type": "string", "const": "vpnservice:allow-start-vpn"}, {"description": "Enables the stop_vpn command without any pre-configured scope.", "type": "string", "const": "vpnservice:allow-stop-vpn"}, {"description": "Denies the ping command without any pre-configured scope.", "type": "string", "const": "vpnservice:deny-ping"}, {"description": "Denies the prepare_vpn command without any pre-configured scope.", "type": "string", "const": "vpnservice:deny-prepare-vpn"}, {"description": "Denies the register_listener command without any pre-configured scope.", "type": "string", "const": "vpnservice:deny-register-listener"}, {"description": "Denies the registerListener command without any pre-configured scope.", "type": "string", "const": "vpnservice:deny-registerListener"}, {"description": "Denies the start_vpn command without any pre-configured scope.", "type": "string", "const": "vpnservice:deny-start-vpn"}, {"description": "Denies the stop_vpn command without any pre-configured scope.", "type": "string", "const": "vpnservice:deny-stop-vpn"}]}, "Value": {"description": "All supported ACL values.", "anyOf": [{"description": "Represents a null JSON value.", "type": "null"}, {"description": "Represents a [`bool`].", "type": "boolean"}, {"description": "Represents a valid ACL [`Number`].", "allOf": [{"$ref": "#/definitions/Number"}]}, {"description": "Represents a [`String`].", "type": "string"}, {"description": "Represents a list of other [`Value`]s.", "type": "array", "items": {"$ref": "#/definitions/Value"}}, {"description": "Represents a map of [`String`] keys to [`Value`]s.", "type": "object", "additionalProperties": {"$ref": "#/definitions/Value"}}]}, "Number": {"description": "A valid ACL number.", "anyOf": [{"description": "Represents an [`i64`].", "type": "integer", "format": "int64"}, {"description": "Represents a [`f64`].", "type": "number", "format": "double"}]}, "Target": {"description": "Platform target.", "oneOf": [{"description": "MacOS.", "type": "string", "enum": ["macOS"]}, {"description": "Windows.", "type": "string", "enum": ["windows"]}, {"description": "Linux.", "type": "string", "enum": ["linux"]}, {"description": "Android.", "type": "string", "enum": ["android"]}, {"description": "iOS.", "type": "string", "enum": ["iOS"]}]}, "ShellScopeEntryAllowedArg": {"description": "A command argument allowed to be executed by the webview API.", "anyOf": [{"description": "A non-configurable argument that is passed to the command in the order it was specified.", "type": "string"}, {"description": "A variable that is set while calling the command from the webview API.", "type": "object", "required": ["validator"], "properties": {"raw": {"description": "Marks the validator as a raw regex, meaning the plugin should not make any modification at runtime.\n\nThis means the regex will not match on the entire string by default, which might be exploited if your regex allow unexpected input to be considered valid. When using this option, make sure your regex is correct.", "default": false, "type": "boolean"}, "validator": {"description": "[regex] validator to require passed values to conform to an expected input.\n\nThis will require the argument value passed to this variable to match the `validator` regex before it will be executed.\n\nThe regex string is by default surrounded by `^...$` to match the full string. For example the `https?://\\w+` regex would be registered as `^https?://\\w+$`.\n\n[regex]: <https://docs.rs/regex/latest/regex/#syntax>", "type": "string"}}, "additionalProperties": false}]}, "ShellScopeEntryAllowedArgs": {"description": "A set of command arguments allowed to be executed by the webview API.\n\nA value of `true` will allow any arguments to be passed to the command. `false` will disable all arguments. A list of [`ShellScopeEntryAllowedArg`] will set those arguments as the only valid arguments to be passed to the attached command configuration.", "anyOf": [{"description": "Use a simple boolean to allow all or disable all arguments to this command configuration.", "type": "boolean"}, {"description": "A specific set of [`ShellScopeEntryAllowedArg`] that are valid to call for the command configuration.", "type": "array", "items": {"$ref": "#/definitions/ShellScopeEntryAllowedArg"}}]}}}