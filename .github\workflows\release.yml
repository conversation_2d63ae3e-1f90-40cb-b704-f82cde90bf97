name: EasyTier Release

on:
  workflow_dispatch:
    inputs:
      core_run_id:
        description: 'The run id of EasyTier-Core Action in EasyTier repo'
        type: number
        default: 10322498549
        required: true
      gui_run_id:
        description: 'The run id of EasyTier-GUI Action in EasyTier repo'
        type: number
        default: 10322498557
        required: true
      mobile_run_id:
        description: 'The run id of EasyTier-Mobile Action in EasyTier repo'
        type: number
        default: 10322498555
        required: true
      version:
        description: 'Version for this release'
        type: string
        default: 'v2.3.0'
        required: true
      make_latest:
        description: 'Mark this release as latest'
        type: boolean
        default: true
        required: true

permissions:
  contents: write

jobs:
  release:
    if: contains('["KKRainbow"]', github.actor)
    runs-on: ubuntu-latest
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4

      - name: Download Core Artifact
        uses: dawidd6/action-download-artifact@v6
        with:
          github_token: ${{secrets.GITHUB_TOKEN}}
          run_id: ${{ inputs.core_run_id }}
          repo: EasyTier/EasyTier
          path: release_assets

      - name: Download GUI Artifact
        uses: dawidd6/action-download-artifact@v6
        with:
          github_token: ${{secrets.GITHUB_TOKEN}}
          run_id: ${{ inputs.gui_run_id }}
          repo: EasyTier/EasyTier
          path: release_assets_nozip

      - name: Download GUI Artifact
        uses: dawidd6/action-download-artifact@v6
        with:
          github_token: ${{secrets.GITHUB_TOKEN}}
          run_id: ${{ inputs.mobile_run_id }}
          repo: EasyTier/EasyTier
          path: release_assets_nozip

      - name: Zip release assets
        env:
          VERSION: ${{ inputs.version }}
        run: |
          mkdir zipped_assets

          find release_assets_nozip -type f -exec mv {} zipped_assets \;
          ls -l -R ./zipped_assets

          cd release_assets
          ls -l -R ./
          chmod -R 755 .
          for x in `ls`; do
            zip ../zipped_assets/$x-${VERSION}.zip $x/*;
          done

      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          name: ${{ inputs.version }}
          draft: true
          files: |
            ./zipped_assets/*
          token: ${{ secrets.GITHUB_TOKEN }}
          tag_name: ${{ inputs.version }}