{"name": "tauri-plugin-vpnservice-api", "version": "0.0.0", "author": "You", "description": "", "type": "module", "types": "./dist-js/index.d.ts", "main": "./dist-js/index.cjs", "module": "./dist-js/index.js", "exports": {"types": "./dist-js/index.d.ts", "import": "./dist-js/index.js", "require": "./dist-js/index.cjs"}, "files": ["dist-js", "README.md"], "scripts": {"build": "rollup -c", "prepublishOnly": "yarn build", "pretest": "yarn build"}, "dependencies": {"@tauri-apps/api": "2.0.0-rc.0"}, "devDependencies": {"@rollup/plugin-typescript": "^11.1.6", "rollup": "^4.20.0", "tslib": "^2.6.3", "typescript": "^5.5.4"}}