# EasyTier 代理转发功能部署指南

## 概述

本指南详细说明如何部署和使用EasyTier的代理转发功能，实现开发人员在A设备上配置B设备的端口代理转发。

## 部署架构

```
[开发人员A设备] <--P2P网络--> [B设备] <--局域网--> [目标服务器]
     |                        |                    |
  代理客户端                代理服务器           实际服务
  监听本地端口              转发到目标            (SSH, HTTP, DB等)
```

## 前置条件

1. **网络连通性**: A设备和B设备能够建立P2P连接
2. **防火墙配置**: 允许EasyTier监听端口通信
3. **权限要求**: 如果使用特权端口(<1024)需要管理员权限
4. **目标服务**: B设备能够访问目标服务器

## 步骤1: 准备配置文件

### A设备配置 (代理客户端)

创建 `easytier_client.toml`:

```toml
# 基本网络配置
instance_name = "proxy_client"
ipv4 = "*************"
network_name = "dev_network"
network_secret = "your_secure_secret_key"

# 代理转发配置
[proxy_forward.environments.production]
server = "*************"  # B设备局域网中的生产服务器
port = [
    "2222-22",     # SSH访问: localhost:2222 -> *************:22
    "3306-3306",   # MySQL: localhost:3306 -> *************:3306
    "8080-80",     # HTTP: localhost:8080 -> *************:80
    "8443-443"     # HTTPS: localhost:8443 -> *************:443
]

[proxy_forward.environments.staging]
server = "*************"  # 测试服务器
port = [
    "2223-22",     # SSH访问测试服务器
    "8081-80"      # 测试环境HTTP
]

[proxy_forward.environments.database]
server = "*************"  # 数据库服务器
port = [
    "5432-5432",   # PostgreSQL
    "6379-6379",   # Redis
    "27017-27017"  # MongoDB
]

# EasyTier网络配置
listeners = ["tcp://0.0.0.0:11010", "udp://0.0.0.0:11010"]
peers = ["tcp://B_DEVICE_PUBLIC_IP:11010"]

# 可选配置
# no_tun = true          # 如果不需要虚拟网卡
# log_level = "info"     # 日志级别
```

### B设备配置 (代理服务器)

创建 `easytier_server.toml`:

```toml
# 基本网络配置
instance_name = "proxy_server"
ipv4 = "*************"
network_name = "dev_network"
network_secret = "your_secure_secret_key"

# EasyTier网络配置
listeners = ["tcp://0.0.0.0:11010", "udp://0.0.0.0:11010"]

# 可选: 如果B设备也需要连接其他节点
# peers = ["tcp://other_peer:11010"]

# 日志配置
log_level = "info"
```

## 步骤2: 启动服务

### 在B设备上启动代理服务器

```bash
# 方法1: 使用配置文件
easytier-core -c easytier_server.toml

# 方法2: 使用命令行参数
easytier-core \
  --instance-name proxy_server \
  --ipv4 ************* \
  --network-name dev_network \
  --network-secret your_secure_secret_key \
  --listeners tcp://0.0.0.0:11010
```

### 在A设备上启动代理客户端

```bash
# 方法1: 使用配置文件
easytier-core -c easytier_client.toml

# 方法2: 使用命令行参数 (基本配置)
easytier-core \
  --instance-name proxy_client \
  --ipv4 ************* \
  --network-name dev_network \
  --network-secret your_secure_secret_key \
  --listeners tcp://0.0.0.0:11010 \
  --peers tcp://B_DEVICE_PUBLIC_IP:11010
```

## 步骤3: 验证连接

### 检查P2P连接状态

```bash
# 在A设备上检查连接状态
easytier-cli peer

# 应该看到B设备在线
# Peer ID: xxx, IP: *************, Status: Connected
```

### 测试代理转发

```bash
# 测试SSH连接
ssh user@localhost -p 2222

# 测试HTTP服务
curl http://localhost:8080

# 测试数据库连接
mysql -h localhost -P 3306 -u username -p

# 测试PostgreSQL
psql -h localhost -p 5432 -U username -d database
```

## 步骤4: 监控和管理

### 使用代理转发CLI工具

```bash
# 列出当前配置
proxy-forward-cli list -c easytier_client.toml

# 添加新的代理环境
proxy-forward-cli add \
  -c easytier_client.toml \
  -e newserver \
  -s ************* \
  -p 9000-80 -p 9001-443

# 验证配置
proxy-forward-cli validate -c easytier_client.toml

# 生成示例配置
proxy-forward-cli example -o example.toml
```

### 查看性能指标

```bash
# 查看连接统计
easytier-cli stats

# 查看详细日志
tail -f /var/log/easytier.log
```

## 常见使用场景

### 场景1: 开发环境访问

开发人员需要访问内网的开发服务器：

```toml
[proxy_forward.environments.dev]
server = "**************"
port = [
    "3000-3000",   # Node.js开发服务器
    "8000-8000",   # Python Django
    "4200-4200",   # Angular开发服务器
    "3001-3001"    # React开发服务器
]
```

使用方式：
```bash
# 访问Node.js应用
curl http://localhost:3000

# 访问Django应用
curl http://localhost:8000/admin/
```

### 场景2: 数据库管理

DBA需要管理内网数据库：

```toml
[proxy_forward.environments.databases]
server = "**************"
port = [
    "15432-5432",   # PostgreSQL主库
    "16379-6379",   # Redis缓存
    "13306-3306"    # MySQL从库
]
```

使用方式：
```bash
# 连接PostgreSQL
psql -h localhost -p 15432 -U admin

# 连接Redis
redis-cli -h localhost -p 16379

# 连接MySQL
mysql -h localhost -P 13306 -u root -p
```

### 场景3: 运维监控

运维人员需要访问监控系统：

```toml
[proxy_forward.environments.monitoring]
server = "**************"
port = [
    "9090-9090",   # Prometheus
    "3000-3000",   # Grafana
    "9093-9093",   # Alertmanager
    "8080-8080"    # 自定义监控面板
]
```

## 安全考虑

### 网络安全

1. **加密通信**: 所有P2P通信都经过加密
2. **密钥管理**: 使用强密码作为network_secret
3. **访问控制**: 限制代理端口的访问来源

### 配置安全

```toml
# 使用强密码
network_secret = "complex_random_secret_key_2024"

# 限制监听接口
listeners = ["tcp://127.0.0.1:11010"]  # 仅本地访问

# 使用非标准端口
listeners = ["tcp://0.0.0.0:12345"]
```

### 防火墙配置

```bash
# A设备防火墙规则
iptables -A INPUT -p tcp --dport 11010 -j ACCEPT
iptables -A INPUT -p tcp --dport 2222:9999 -s 127.0.0.1 -j ACCEPT

# B设备防火墙规则
iptables -A INPUT -p tcp --dport 11010 -j ACCEPT
iptables -A OUTPUT -p tcp --dport 22,80,443,3306,5432 -j ACCEPT
```

## 故障排除

### 常见问题

1. **连接失败**
   ```bash
   # 检查网络连通性
   ping B_DEVICE_IP
   telnet B_DEVICE_IP 11010
   
   # 检查防火墙
   iptables -L
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :2222
   lsof -i :2222
   ```

3. **权限问题**
   ```bash
   # 使用sudo运行
   sudo easytier-core -c config.toml
   
   # 或使用非特权端口 (>1024)
   ```

### 日志分析

```bash
# 查看详细日志
easytier-core -c config.toml --log-level debug

# 过滤代理相关日志
grep "proxy_forward" /var/log/easytier.log

# 实时监控连接
tail -f /var/log/easytier.log | grep "connection"
```

## 性能优化

### 网络优化

```toml
# 启用UDP传输以提高性能
listeners = ["udp://0.0.0.0:11010"]

# 配置多个监听端口
listeners = [
    "tcp://0.0.0.0:11010",
    "udp://0.0.0.0:11010",
    "tcp://0.0.0.0:11011"
]
```

### 系统优化

```bash
# 增加文件描述符限制
ulimit -n 65536

# 优化TCP参数
echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf
sysctl -p
```

## 维护和更新

### 配置备份

```bash
# 备份配置文件
cp easytier_client.toml easytier_client.toml.backup.$(date +%Y%m%d)

# 版本控制
git add easytier_client.toml
git commit -m "Update proxy forward config"
```

### 服务管理

```bash
# 创建systemd服务
cat > /etc/systemd/system/easytier-proxy.service << EOF
[Unit]
Description=EasyTier Proxy Forward Service
After=network.target

[Service]
Type=simple
User=easytier
ExecStart=/usr/local/bin/easytier-core -c /etc/easytier/config.toml
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
systemctl enable easytier-proxy
systemctl start easytier-proxy
```
