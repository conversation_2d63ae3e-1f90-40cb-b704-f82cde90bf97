network: Network
networking_method: Networking Method
public_server: Public Server
manual: Manual
standalone: Standalone
virtual_ipv4: Virtual IPv4
virtual_ipv4_dhcp: DHCP
network_name: Network Name
network_secret: Network Secret
public_server_url: Public Server URL
peer_urls: Peer URLs
proxy_cidrs: Subnet Proxy CIDRs
enable_vpn_portal: Enable VPN Portal
vpn_portal_listen_port: VPN Portal Listen Port
vpn_portal_client_network: Client Sub Network
dev_name: TUN interface name
advanced_settings: Advanced Settings
basic_settings: Basic Settings
listener_urls: Listener URLs
rpc_port: RPC Port
config_network: Config Network
running: Running
error_msg: Error Message
detail: Detail
add_new_network: New Network
del_cur_network: Delete Current Network
select_network: Select Network
network_instances: Network Instances
instance_id: Instance ID
network_infos: Network Infos
parse_network_config: Parse Network Config
retain_network_instance: Retain Network Instance
collect_network_infos: Collect Network Infos
settings: Settings
exchange_language: 切换中文
logging: Logging
logging_level_info: Info
logging_level_debug: Debug
logging_level_warn: Warn
logging_level_trace: Trace
logging_level_off: Off
logging_open_dir: Open Log Directory
logging_copy_dir: Copy Log Path
disable_auto_launch: Disable Launch on Reboot
enable_auto_launch: Enable Launch on Reboot
exit: Exit
use_latency_first: Latency First Mode
chips_placeholder: 'e.g: {0}, press Enter to add'
hostname_placeholder: 'Leave blank and default to host name: {0}'
dev_name_placeholder: 'Note: When multiple networks use the same TUN interface name at the same time, there will be a conflict when setting the TUN''s IP. Leave blank to automatically generate a random name.'
off_text: Press to disable
on_text: Press to enable
show_config: Show Config
close: Close
my_node_info: My Node Info
peer_count: Connected
upload: Upload
download: Download
show_vpn_portal_config: Show VPN Portal Config
vpn_portal_config: VPN Portal Config
show_event_log: Show Event Log
event_log: Event Log
peer_info: Peer Info
route_cost: Route Cost
hostname: Hostname
latency: Latency
upload_bytes: Upload
download_bytes: Download
loss_rate: Loss Rate

flags_switch: Feature Switch

latency_first: Enable Latency-First Mode
latency_first_help: Ignore hop count and select the path with the lowest total latency

use_smoltcp: Use User-Space Protocol Stack
use_smoltcp_help: Use a user-space TCP/IP stack to avoid issues with operating system firewalls blocking subnet or KCP proxy functionality.

enable_kcp_proxy: Enable KCP Proxy
enable_kcp_proxy_help: Convert TCP traffic to KCP traffic to reduce latency and boost transmission speed.

disable_kcp_input: Disable KCP Input
disable_kcp_input_help: Disable inbound KCP traffic, while nodes with KCP proxy enabled continue to connect using TCP.

disable_p2p: Disable P2P
disable_p2p_help: Disable P2P mode; route all traffic through a manually specified relay server.

bind_device: Bind to Physical Device Only
bind_device_help: Use only the physical network interface to prevent EasyTier from connecting via virtual networks.

no_tun: No TUN Mode
no_tun_help: Do not use a TUN interface, suitable for environments without administrator privileges. This node is only accessible; accessing other nodes requires SOCKS5.

enable_exit_node: Enable Exit Node
enable_exit_node_help: Allow this node to be an exit node

relay_all_peer_rpc: Relay RPC Packets
relay_all_peer_rpc_help: |
  Relay all peer rpc packets, even if the peer is not in the relay network whitelist.
  This can help peers not in relay network whitelist to establish p2p connection.

multi_thread: Multi Thread
multi_thread_help: Use multi-thread runtime

proxy_forward_by_system: System Forward
proxy_forward_by_system_help: Forward packet to proxy networks via system kernel, disable internal nat for network proxy

disable_encryption: Disable Encryption
disable_encryption_help: Disable encryption for peers communication, default is false, must be same with peers

disable_udp_hole_punching: Disable UDP Hole Punching
disable_udp_hole_punching_help: Disable udp hole punching

enable_magic_dns: Enable Magic DNS
enable_magic_dns_help: |
  Enable magic dns, all nodes in the network can access each other by domain name, e.g.: node1.et.net.

relay_network_whitelist: Network Whitelist
relay_network_whitelist_help: |
  Only forward traffic from the whitelist networks, supporting wildcard strings, multiple network names can be separated by spaces.
  If this parameter is empty, forwarding is disabled. By default, all networks are allowed.
  e.g.: '*' (all networks), 'def*' (networks with the prefix 'def'), 'net1 net2' (only allow net1 and net2)

manual_routes: Manual Route
manual_routes_help: |
  Assign routes cidr manually, will disable subnet proxy and wireguard routes propagated from peers. e.g.:***********/16

socks5: Socks5 Server
socks5_help: |
  Enable socks5 server, allow socks5 client to access virtual network. format: <port>, e.g.: 1080

exit_nodes: Exit Nodes
exit_nodes_help: Exit nodes to forward all traffic to, a virtual ipv4 address, priority is determined by the order of the list

mtu: MTU
mtu_help: |
  MTU of the TUN device, default is 1380 for non-encryption, 1360 for encryption. Range:400-1380
mtu_placeholder: Leave blank as default value 1380

mapped_listeners: Map Listeners
mapped_listeners_help: |
  Manually specify the public address of the listener, other nodes can use this address to connect to this node.
  e.g.: tcp://***************:11223, can specify multiple.

status:
  version: Version
  local: Local
  server: Server
  relay: Relay

run_network: Run Network
stop_network: Stop Network
network_running: running
network_stopped: stopped
dhcp_experimental_warning: Experimental warning! if there is an IP conflict in the network when using DHCP, the IP will be automatically changed.

tray:
  show: Show / Hide
  exit: Exit

about:
  title: About
  version: Version
  author: Author
  homepage: Homepage
  license: License
  description: 'EasyTier is a simple, safe and decentralized VPN networking solution implemented with the Rust language and Tokio framework.'
  check_update: Check Update

event:
  Unknown: Unknown
  TunDeviceReady: TunDeviceReady
  TunDeviceError: TunDeviceError
  PeerAdded: PeerAdded
  PeerRemoved: PeerRemoved
  PeerConnAdded: PeerConnAdded
  PeerConnRemoved: PeerConnRemoved
  ListenerAdded: ListenerAdded
  ListenerAddFailed: ListenerAddFailed
  ListenerAcceptFailed: ListenerAcceptFailed
  ConnectionAccepted: ConnectionAccepted
  ConnectionError: ConnectionError
  Connecting: Connecting
  ConnectError: ConnectError
  VpnPortalClientConnected: VpnPortalClientConnected
  VpnPortalClientDisconnected: VpnPortalClientDisconnected
  DhcpIpv4Changed: DhcpIpv4Changed
  DhcpIpv4Conflicted: DhcpIpv4Conflicted
  PortForwardAdded: PortForwardAdded
