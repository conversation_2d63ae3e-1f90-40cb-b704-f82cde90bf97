name: EasyTier Test

on:
  push:
    branches: ["develop", "main"]
  pull_request:
    branches: ["develop", "main"]

env:
  CARGO_TERM_COLOR: always

defaults:
  run:
    # necessary for windows
    shell: bash

jobs:
  pre_job:
    # continue-on-error: true # Uncomment once integration is finished
    runs-on: ubuntu-latest
    # Map a step output to a job output
    outputs:
      should_skip: ${{ steps.skip_check.outputs.should_skip }}
    steps:
      - id: skip_check
        uses: fkirc/skip-duplicate-actions@v5
        with:
          # All of these options are optional, so you can remove them if you are happy with the defaults
          concurrent_skipping: 'never'
          skip_after_successful_duplicate: 'true'
          paths: '["Cargo.toml", "Cargo.lock", "easytier/**", ".github/workflows/test.yml"]'
  test:
    runs-on: ubuntu-22.04
    needs: pre_job
    if: needs.pre_job.outputs.should_skip != 'true'    
    steps:
      - uses: actions/checkout@v3

      - name: Setup protoc
        uses: arduino/setup-protoc@v2
        with:
          # GitHub repo token to use to avoid rate limiter
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup tools for test
        run: sudo apt install bridge-utils

      - name: Setup system for test
        run: |
          sudo modprobe br_netfilter
          sudo sysctl net.bridge.bridge-nf-call-iptables=0
          sudo sysctl net.bridge.bridge-nf-call-ip6tables=0
          sudo sysctl net.ipv6.conf.lo.disable_ipv6=0
          sudo ip addr add 2001:db8::2/64 dev lo

      - uses: actions/setup-node@v4
        with:
          node-version: 21

      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 9
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install frontend dependencies
        run: |
          pnpm -r install
          pnpm -r --filter "./easytier-web/*"  build

      - name: Cargo cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo
            ./target
          key: ${{ runner.os }}-cargo-test-${{ hashFiles('**/Cargo.lock') }}

      - name: Run tests
        run: |
          sudo -E env "PATH=$PATH" cargo test --no-default-features --features=full --verbose -- --test-threads=1 --nocapture
          sudo chown -R $USER:$USER ./target
          sudo chown -R $USER:$USER ~/.cargo
