network: 网络
networking_method: 网络方式
public_server: 公共服务器
manual: 手动
standalone: 独立
virtual_ipv4: 虚拟IPv4地址
virtual_ipv4_dhcp: DHCP
network_name: 网络名称
network_secret: 网络密码
public_server_url: 公共服务器地址
peer_urls: 对等节点地址
proxy_cidrs: 子网代理CIDR
enable_vpn_portal: 启用VPN门户
vpn_portal_listen_port: 监听端口
vpn_portal_client_network: 客户端子网
dev_name: TUN接口名称
advanced_settings: 高级设置
basic_settings: 基础设置
listener_urls: 监听地址
rpc_port: RPC端口
config_network: 配置网络
running: 运行中
error_msg: 错误信息
detail: 详情
add_new_network: 添加新网络
del_cur_network: 删除当前网络
select_network: 选择网络
network_instances: 网络实例
instance_id: 实例ID
network_infos: 网络信息
parse_network_config: 解析网络配置
retain_network_instance: 保留网络实例
collect_network_infos: 收集网络信息
settings: 设置
exchange_language: Switch to English
logging: 日志
logging_level_info: 信息
logging_level_debug: 调试
logging_level_warn: 警告
logging_level_trace: 跟踪
logging_level_off: 关闭
logging_open_dir: 打开日志目录
logging_copy_dir: 复制日志路径
disable_auto_launch: 关闭开机自启
enable_auto_launch: 开启开机自启
exit: 退出
chips_placeholder: 例如： {0}, 按回车添加
hostname_placeholder: '留空默认为主机名: {0}'
dev_name_placeholder: 注意：当多个网络同时使用相同的TUN接口名称时，将会在设置TUN的IP时产生冲突，留空以自动生成随机名称
off_text: 点击关闭
on_text: 点击开启
show_config: 显示配置
close: 关闭

use_latency_first: 延迟优先模式
my_node_info: 当前节点信息
peer_count: 已连接
upload: 上传
download: 下载
show_vpn_portal_config: 显示VPN门户配置
vpn_portal_config: VPN门户配置
show_event_log: 显示事件日志
event_log: 事件日志
peer_info: 节点信息
hostname: 主机名
route_cost: 路由
latency: 延迟
upload_bytes: 上传
download_bytes: 下载
loss_rate: 丢包率

flags_switch: 功能开关

latency_first: 开启延迟优先模式
latency_first_help: 忽略中转跳数，选择总延迟最低的路径

use_smoltcp: 使用用户态协议栈
use_smoltcp_help: 使用用户态 TCP/IP 协议栈，避免操作系统防火墙问题导致无法子网代理 / KCP代理。

enable_kcp_proxy: 启用 KCP 代理
enable_kcp_proxy_help: 将 TCP 流量转为 KCP 流量，降低传输延迟，提升传输速度。

disable_kcp_input: 禁用 KCP 输入
disable_kcp_input_help: 禁用 KCP 入站流量，其他开启 KCP 代理的节点仍然使用 TCP 连接到本节点。

disable_p2p: 禁用 P2P
disable_p2p_help: 禁用 P2P 模式，所有流量通过手动指定的服务器中转。

bind_device: 仅使用物理网卡
bind_device_help: 仅使用物理网卡，避免 EasyTier 通过其他虚拟网建立连接。

no_tun: 无 TUN 模式
no_tun_help: 不使用 TUN 网卡，适合无管理员权限时使用。本节点仅允许被访问。访问其他节点需要使用 SOCK5

enable_exit_node: 启用出口节点
enable_exit_node_help: 允许此节点成为出口节点

relay_all_peer_rpc: 转发RPC包
relay_all_peer_rpc_help: |
  允许转发所有对等节点的RPC数据包，即使对等节点不在转发网络白名单中。
  这可以帮助白名单外网络中的对等节点建立P2P连接。

multi_thread: 启用多线程
multi_thread_help: 使用多线程运行时

proxy_forward_by_system: 系统转发
proxy_forward_by_system_help: 通过系统内核转发子网代理数据包，禁用内置NAT

disable_encryption: 禁用加密
disable_encryption_help: 禁用对等节点通信的加密，默认为false，必须与对等节点相同

disable_udp_hole_punching: 禁用UDP打洞
disable_udp_hole_punching_help: 禁用UDP打洞功能

enable_magic_dns: 启用魔法DNS
enable_magic_dns_help: |
  启用魔法DNS，允许通过EasyTier的DNS服务器访问其他节点的虚拟IPv4地址， 如 node1.et.net。

relay_network_whitelist: 网络白名单
relay_network_whitelist_help: |
  仅转发白名单网络的流量，支持通配符字符串。多个网络名称间可以使用英文空格间隔。
  如果该参数为空，则禁用转发。默认允许所有网络。
  例如：'*'（所有网络），'def*'（以def为前缀的网络），'net1 net2'（只允许net1和net2）

manual_routes: 自定义路由
manual_routes_help: 手动分配路由CIDR，将禁用子网代理和从对等节点传播的wireguard路由。例如：***********/16

socks5: socks5服务器
socks5_help: |
  启用 socks5 服务器，允许 socks5 客户端访问虚拟网络. 格式: <端口>，例如：1080

exit_nodes: 出口节点列表
exit_nodes_help: 转发所有流量的出口节点，虚拟IPv4地址，优先级由列表顺序决定

mtu: MTU
mtu_help: |
  TUN设备的MTU，默认为非加密时为1380，加密时为1360。范围：400-1380
mtu_placeholder: 留空为默认值1380

mapped_listeners: 监听映射
mapped_listeners_help: |
  手动指定监听器的公网地址，其他节点可以使用该地址连接到本节点。
  例如：tcp://***************:11223，可以指定多个。

status:
  version: 内核版本
  local: 本机
  server: 服务器
  relay: 中继

run_network: 运行网络
stop_network: 停止网络
network_running: 运行中
network_stopped: 已停止
dhcp_experimental_warning: 实验性警告！使用DHCP时如果组网环境中发生IP冲突，将自动更改IP。

tray:
  show: 显示 / 隐藏
  exit: 退出

about:
  title: 关于
  version: 版本
  author: 作者
  homepage: 主页
  license: 许可证
  description: 一个简单、安全、去中心化的内网穿透 VPN 组网方案，使用 Rust 语言和 Tokio 框架实现。
  check_update: 检查更新

event:
  Unknown: 未知
  TunDeviceReady: Tun设备就绪
  TunDeviceError: Tun设备错误
  PeerAdded: 对端添加
  PeerRemoved: 对端移除
  PeerConnAdded: 对端连接添加
  PeerConnRemoved: 对端连接移除
  ListenerAdded: 监听器添加
  ListenerAddFailed: 监听器添加失败
  ListenerAcceptFailed: 监听器接受连接失败
  ConnectionAccepted: 连接已接受
  ConnectionError: 连接错误
  Connecting: 正在连接
  ConnectError: 连接错误
  VpnPortalClientConnected: VPN门户客户端已连接
  VpnPortalClientDisconnected: VPN门户客户端已断开连接
  DhcpIpv4Changed: DHCP IPv4地址更改
  DhcpIpv4Conflicted: DHCP IPv4地址冲突
  PortForwardAdded: 端口转发添加
