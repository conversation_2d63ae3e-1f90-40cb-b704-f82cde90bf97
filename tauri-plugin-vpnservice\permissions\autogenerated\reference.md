## Default Permission

Default permissions for the plugin

- `allow-ping`
- `allow-start-vpn`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`vpnservice:allow-ping`

</td>
<td>

Enables the ping command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`vpnservice:deny-ping`

</td>
<td>

Denies the ping command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`vpnservice:allow-prepare-vpn`

</td>
<td>

Enables the prepare_vpn command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`vpnservice:deny-prepare-vpn`

</td>
<td>

Denies the prepare_vpn command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`vpnservice:allow-registerListener`

</td>
<td>

Enables the registerListener command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`vpnservice:deny-registerListener`

</td>
<td>

Denies the registerListener command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`vpnservice:allow-register-listener`

</td>
<td>

Enables the register_listener command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`vpnservice:deny-register-listener`

</td>
<td>

Denies the register_listener command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`vpnservice:allow-start-vpn`

</td>
<td>

Enables the start_vpn command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`vpnservice:deny-start-vpn`

</td>
<td>

Denies the start_vpn command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`vpnservice:allow-stop-vpn`

</td>
<td>

Enables the stop_vpn command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`vpnservice:deny-stop-vpn`

</td>
<td>

Denies the stop_vpn command without any pre-configured scope.

</td>
</tr>
</table>
