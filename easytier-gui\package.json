{"name": "easytier-gui", "type": "module", "version": "2.2.4", "private": true, "packageManager": "pnpm@9.12.1+sha512.e5a7e52a4183a02d5931057f7a0dbff9d5e9ce3161e33fa68ae392125b79282a8a8a470a51dfc8a0ed86221442eb2fb57019b0990ed24fab519bf0e1bc5ccfc4", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri", "lint": "eslint . --ignore-pattern src-tauri", "lint:fix": "eslint . --ignore-pattern src-tauri --fix"}, "dependencies": {"@primevue/themes": "4.3.3", "@tauri-apps/plugin-autostart": "2.0.0", "@tauri-apps/plugin-clipboard-manager": "2.0.0", "@tauri-apps/plugin-os": "2.0.0", "@tauri-apps/plugin-process": "2.0.0", "@tauri-apps/plugin-shell": "2.0.1", "@vueuse/core": "^11.2.0", "aura": "link:@primevue\\themes\\aura", "easytier-frontend-lib": "workspace:*", "ip-num": "1.5.1", "pinia": "^2.2.4", "primevue": "4.3.3", "tauri-plugin-vpnservice-api": "workspace:*", "vue": "^3.5.12", "vue-router": "^4.4.5"}, "devDependencies": {"@antfu/eslint-config": "^3.7.3", "@intlify/unplugin-vue-i18n": "^5.2.0", "@primevue/auto-import-resolver": "4.3.3", "@tauri-apps/api": "2.1.0", "@tauri-apps/cli": "2.1.0", "@types/default-gateway": "^7.2.2", "@types/node": "^22.7.4", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^5.1.4", "@vue-macros/volar": "0.30.5", "autoprefixer": "^10.4.20", "cidr-tools": "^11.0.2", "default-gateway": "^7.2.2", "eslint": "^9.12.0", "eslint-plugin-format": "^0.1.2", "postcss": "^8.4.47", "tailwindcss": "=3.4.17", "typescript": "^5.6.2", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "unplugin-vue-macros": "^2.13.3", "unplugin-vue-markdown": "^0.26.2", "unplugin-vue-router": "^0.10.8", "uuid": "^10.0.0", "vite": "^5.4.8", "vite-plugin-vue-devtools": "^7.4.6", "vite-plugin-vue-layouts": "^0.11.0", "vue-i18n": "^10.0.0", "vue-tsc": "^2.1.10"}}