# EasyTier 代理转发功能实现

## 功能概述

本实现为EasyTier添加了代理转发功能，允许开发人员在A设备上配置B设备的端口代理转发，实现跨网络的服务访问。

## 核心特性

1. **配置驱动**: 通过TOML配置文件定义代理转发规则
2. **多环境支持**: 支持配置多个目标环境（服务器组）
3. **端口映射**: 灵活的本地端口到远程端口映射
4. **P2P通信**: 基于EasyTier现有的P2P网络进行数据传输
5. **自动发现**: 自动发现可用的对等节点进行代理

## 架构设计

### 组件结构

```
proxy_forward_manager.rs     # 代理转发管理器（主控制器）
├── proxy_forward_client.rs  # A设备端代理客户端
├── proxy_forward_server.rs  # B设备端代理服务器
└── proxy_forward_connector.rs # 连接器实现
```

### 工作流程

1. **配置解析**: A设备读取配置文件，解析代理转发规则
2. **服务启动**: 
   - A设备启动代理客户端，监听本地端口
   - B设备启动代理服务器，等待代理请求
3. **连接建立**: 
   - 客户端接收到连接时，通过P2P网络向B设备发送代理请求
   - B设备接收请求后，连接到目标服务器
4. **数据转发**: 双向转发客户端和目标服务器之间的数据

## 配置格式

### 基本配置结构

```toml
[proxy_forward.environments.环境名称]
server = "目标服务器IP"
port = ["本地端口-远程端口", ...]
```

### 配置示例

```toml
[proxy_forward.environments.server]
server = "*************"
port = [
    "400-22",      # SSH访问
    "401-3306",    # MySQL数据库
    "402-80",      # HTTP服务
    "403-443"      # HTTPS服务
]

[proxy_forward.environments.jumpserver]
server = "*************"
port = ["500-22"]
```

## 使用方法

### 1. A设备配置（代理客户端）

创建配置文件 `easytier_client.toml`:

```toml
instance_name = "proxy_client"
ipv4 = "*************"
network_name = "default"
network_secret = "your_secret_key"

[proxy_forward.environments.server]
server = "*************"
port = ["400-22", "401-3306"]

listeners = ["tcp://0.0.0.0:11010"]
peers = ["tcp://b_device_ip:11010"]
```

启动客户端:
```bash
easytier-core -c easytier_client.toml
```

### 2. B设备配置（代理服务器）

创建配置文件 `easytier_server.toml`:

```toml
instance_name = "proxy_server"
ipv4 = "*************"
network_name = "default"
network_secret = "your_secret_key"

listeners = ["tcp://0.0.0.0:11010"]
```

启动服务器:
```bash
easytier-core -c easytier_server.toml
```

### 3. 使用代理服务

配置完成后，可以通过A设备的本地端口访问B设备局域网中的服务：

```bash
# 通过A设备的400端口SSH到B设备局域网的*************:22
ssh user@localhost -p 400

# 通过A设备的401端口连接B设备局域网的MySQL
mysql -h localhost -P 401 -u username -p
```

## 实现细节

### 配置扩展

在 `common/config.rs` 中添加了新的配置结构：

```rust
#[derive(Debug, Clone, Deserialize, Serialize, PartialEq)]
pub struct ProxyForwardEnvironment {
    pub server: String,
    pub port: Vec<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize, PartialEq)]
pub struct ProxyForwardConfig {
    pub environments: HashMap<String, ProxyForwardEnvironment>,
}
```

### RPC协议

在 `proto/peer_rpc.proto` 中定义了代理转发的RPC消息：

```protobuf
message ProxyForwardRequest {
  uint32 session_id = 1;
  common.SocketAddr src_addr = 2;
  common.SocketAddr dst_addr = 3;
  string protocol = 4;
}

service ProxyForwardRpc {
  rpc CreateProxySession(ProxyForwardRequest) returns (ProxyForwardResponse);
  rpc SendData(ProxyForwardData) returns (common.Void);
  rpc CloseSession(ProxyForwardClose) returns (common.Void);
}
```

### 会话管理

- 每个代理连接分配唯一的session_id
- 支持并发多个代理会话
- 自动清理断开的会话

## 测试验证

项目包含了完整的测试用例：

```bash
# 运行配置解析测试
cargo test proxy_forward_config_parsing

# 运行端口映射解析测试  
cargo test test_port_mapping_parsing

# 运行环境配置测试
cargo test test_proxy_forward_environment_creation
```

## 扩展计划

1. **动态配置**: 支持运行时动态添加/删除代理规则
2. **负载均衡**: 支持多个目标服务器的负载均衡
3. **访问控制**: 添加基于IP/用户的访问控制
4. **监控统计**: 添加连接数、流量统计等监控功能
5. **协议支持**: 扩展支持UDP代理转发

## 注意事项

1. 确保A、B设备能够建立P2P连接
2. 配置相同的network_name和network_secret
3. 防火墙需要允许EasyTier的监听端口
4. 目标服务器需要在B设备的网络可达范围内

## 故障排除

1. **连接失败**: 检查P2P网络连接状态
2. **端口冲突**: 确保本地端口未被占用
3. **权限问题**: 确保有权限绑定指定端口
4. **网络不通**: 检查B设备到目标服务器的网络连通性
