{"name": "easytier-frontend-lib", "private": true, "version": "0.0.0", "type": "module", "main": "./dist/easytier-frontend-lib.umd.cjs", "module": "./dist/easytier-frontend-lib.js", "exports": {".": {"import": "./dist/easytier-frontend-lib.js", "require": "./dist/easytier-frontend-lib.umd.cjs"}, "./*.css": "./dist/*.css"}, "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@primevue/themes": "4.3.3", "@vueuse/core": "^11.1.0", "aura": "link:@primevue\\themes\\aura", "axios": "^1.7.7", "floating-vue": "^5.2", "ip-num": "1.5.1", "primeicons": "^7.0.0", "primevue": "4.3.3", "tailwindcss-primeui": "^0.3.4", "ts-md5": "^1.3.1", "uuid": "^11.0.2", "vue": "^3.5.12", "vue-i18n": "^10.0.4"}, "devDependencies": {"@modyfi/vite-plugin-yaml": "^1.1.0", "@types/node": "^22.8.6", "@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "postcss-import": "^16.1.0", "postcss-nested": "^7.0.2", "tailwindcss": "=3.4.17", "typescript": "~5.6.3", "vite": "^5.4.10", "vite-plugin-dts": "^4.3.0", "vue-tsc": "^2.1.10"}}