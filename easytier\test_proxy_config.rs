use std::collections::HashMap;
use easytier::common::config::{TomlConfigLoader, ProxyForwardConfig, ProxyForwardEnvironment};

fn main() {
    let config_str = r#"
instance_name = "test"
ipv4 = "*************"

[proxy_forward.environments.server]
server = "*************"
port = ["400-22", "401-3306"]

[proxy_forward.environments.jumpserver]
server = "*************"
port = ["500-22"]
"#;

    match TomlConfigLoader::new_from_str(config_str) {
        Ok(config) => {
            println!("配置解析成功！");
            
            if let Some(proxy_config) = config.get_proxy_forwards() {
                println!("代理转发配置:");
                for (env_name, env_config) in &proxy_config.environments {
                    println!("  环境: {}", env_name);
                    println!("    服务器: {}", env_config.server);
                    println!("    端口映射: {:?}", env_config.port);
                }
            } else {
                println!("未找到代理转发配置");
            }
        }
        Err(e) => {
            println!("配置解析失败: {}", e);
        }
    }
}
