{"build": {"beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist", "devUrl": "http://localhost:1420"}, "bundle": {"active": true, "targets": "all", "icon": ["icons/icon.png", "icons/icon.rgba", "icons/icon.icns", "icons/icon.ico"], "createUpdaterArtifacts": false}, "productName": "easytier-gui", "version": "2.3.0", "identifier": "com.kkrainbow.easytier", "plugins": {}, "app": {"windows": [{"title": "easytier-gui", "width": 800, "height": 600}], "security": {"csp": null}}}