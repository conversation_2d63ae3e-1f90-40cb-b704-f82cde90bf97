[package]
name = "easytier-gui"
version = "2.3.0"
description = "EasyTier GUI"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.0.0-rc", features = [] }

# enable thunk-rs when compiling for x86_64 or i686 windows
[target.x86_64-pc-windows-msvc.build-dependencies]
thunk-rs = { git = "https://github.com/easytier/thunk.git", default-features = false, features = ["win7"] }

[target.i686-pc-windows-msvc.build-dependencies]
thunk-rs = { git = "https://github.com/easytier/thunk.git", default-features = false, features = ["win7"] }

[dependencies]
# wry 0.47 may crash on android, see https://github.com/EasyTier/EasyTier/issues/527
tauri = { version = "=2.0.6", features = [
    "tray-icon",
    "image-png",
    "image-ico",
    "devtools",
] }

serde = { version = "1", features = ["derive"] }
serde_json = "1"

easytier = { path = "../../easytier" }
tokio = { version = "1", features = ["full"] }
anyhow = "1.0"
chrono = { version = "0.4.37", features = ["serde"] }

once_cell = "1.18.0"
dashmap = "6.0"

privilege = "0.3"
gethostname = "0.5"

dunce = "1.0.4"

tauri-plugin-shell = "2.0"
tauri-plugin-process = "2.0"
tauri-plugin-clipboard-manager = "2.0"
tauri-plugin-positioner = { version = "2.0", features = ["tray-icon"] }
tauri-plugin-vpnservice = { path = "../../tauri-plugin-vpnservice" }
tauri-plugin-os = "2.0"
tauri-plugin-autostart = "2.0"


[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-single-instance = "2.2.3"
