syntax = "proto3";

package tests;

/// The Greeting service. This service is used to generate greetings for various
/// use-cases.
service Greeting {
  // Generates a "hello" greeting based on the supplied info.
  rpc <PERSON><PERSON><PERSON>(SayHelloRequest) returns (SayHelloResponse);
  // Generates a "goodbye" greeting based on the supplied info.
  rpc <PERSON><PERSON>by<PERSON>(SayGoodbyeRequest) returns (SayGoodbyeResponse);
}

// The request for an `Greeting.SayHello` call.
message SayHelloRequest { string name = 1; }

// The response for an `Greeting.SayHello` call.
message SayHelloResponse { string greeting = 1; }

// The request for an `Greeting.SayGoodbye` call.
message SayGoodbyeRequest { string name = 1; }

// The response for an `Greeting.SayGoodbye` call.
message SayGoodbyeResponse { string greeting = 1; }
