use std::{
    net::SocketAddr,
    sync::{
        atomic::{AtomicU32, Ordering},
        Arc, Mutex,
    },
};

use anyhow::{Context, Result};
use dashmap::DashMap;
use tokio::{
    io::{AsyncReadExt, AsyncWriteExt},
    net::{TcpL<PERSON>ener, TcpStream},
    sync::mpsc,
    task::JoinSet,
};
use tracing::{error, info, warn};

use crate::{
    common::{config::ProxyForwardConfig, global_ctx::ArcGlobalCtx, PeerId},
    connector::direct::PeerManagerForDirectConnector,
    peers::peer_manager::PeerManager,
    proto::peer_rpc::ProxyForwardRequest,
};

#[derive(Debug, Clone)]
pub struct ProxySession {
    pub session_id: u32,
    pub src_addr: SocketAddr,
    pub dst_addr: SocketAddr,
    pub protocol: String,
    pub peer_id: PeerId,
}

#[derive(Debug)]
pub struct ProxyForwardClient {
    global_ctx: ArcGlobalCtx,
    peer_manager: Arc<PeerManager>,
    config: ProxyForwardConfig,

    // Session management
    session_counter: AtomicU32,
    active_sessions: Arc<DashMap<u32, ProxySession>>,

    // Task management
    tasks: Arc<Mutex<JoinSet<()>>>,

    // Data channels for each session
    data_senders: Arc<DashMap<u32, mpsc::UnboundedSender<Vec<u8>>>>,
}

impl ProxyForwardClient {
    pub fn new(
        global_ctx: ArcGlobalCtx,
        peer_manager: Arc<PeerManager>,
        config: ProxyForwardConfig,
    ) -> Self {
        Self {
            global_ctx,
            peer_manager,
            config,
            session_counter: AtomicU32::new(1),
            active_sessions: Arc::new(DashMap::new()),
            tasks: Arc::new(Mutex::new(JoinSet::new())),
            data_senders: Arc::new(DashMap::new()),
        }
    }

    pub async fn start(&self) -> Result<()> {
        info!("Starting proxy forward client");

        // Start listeners for each environment
        for (env_name, env_config) in &self.config.environments {
            for port_mapping in &env_config.port {
                self.start_port_listener(env_name, env_config, port_mapping).await?;
            }
        }

        Ok(())
    }

    async fn start_port_listener(
        &self,
        env_name: &str,
        env_config: &crate::common::config::ProxyForwardEnvironment,
        port_mapping: &str,
    ) -> Result<()> {
        // Parse port mapping: "400-22" means local port 400 -> remote port 22
        let parts: Vec<&str> = port_mapping.split('-').collect();
        if parts.len() != 2 {
            return Err(anyhow::anyhow!("Invalid port mapping format: {}", port_mapping));
        }

        let local_port: u16 = parts[0].parse()
            .with_context(|| format!("Invalid local port: {}", parts[0]))?;
        let remote_port: u16 = parts[1].parse()
            .with_context(|| format!("Invalid remote port: {}", parts[1]))?;

        let bind_addr = SocketAddr::from(([0, 0, 0, 0], local_port));
        let listener = TcpListener::bind(bind_addr).await
            .with_context(|| format!("Failed to bind to {}", bind_addr))?;

        info!("Proxy forward client listening on {} for environment {}", bind_addr, env_name);

        // Clone necessary data for the task
        let env_name = env_name.to_string();
        let server_addr = env_config.server.clone();
        let remote_port = remote_port;
        let client = self.clone();

        self.tasks.lock().unwrap().spawn(async move {
            loop {
                match listener.accept().await {
                    Ok((stream, client_addr)) => {
                        info!("New connection from {} for environment {}", client_addr, env_name);

                        let dst_addr = format!("{}:{}", server_addr, remote_port)
                            .parse::<SocketAddr>()
                            .unwrap();

                        if let Err(e) = client.handle_new_connection(stream, client_addr, dst_addr).await {
                            error!("Failed to handle connection: {}", e);
                        }
                    }
                    Err(e) => {
                        error!("Failed to accept connection: {}", e);
                        break;
                    }
                }
            }
        });

        Ok(())
    }

    async fn handle_new_connection(
        &self,
        stream: TcpStream,
        src_addr: SocketAddr,
        dst_addr: SocketAddr,
    ) -> Result<()> {
        // Generate session ID
        let session_id = self.session_counter.fetch_add(1, Ordering::SeqCst);

        // Find target peer (for now, use the first available peer)
        // In a real implementation, you would need to determine which peer
        // can reach the destination address
        let target_peer_id = self.find_target_peer(&dst_addr).await?;

        // Create proxy session
        let session = ProxySession {
            session_id,
            src_addr,
            dst_addr,
            protocol: "tcp".to_string(),
            peer_id: target_peer_id,
        };

        // Send proxy request to target peer
        let success = self.create_proxy_session(&session).await?;
        if !success {
            warn!("Failed to create proxy session {}", session_id);
            return Ok(());
        }

        // Store session
        self.active_sessions.insert(session_id, session.clone());

        // Create data channel
        let (tx, rx) = mpsc::unbounded_channel();
        self.data_senders.insert(session_id, tx);

        // Handle data forwarding
        let client = self.clone();
        let session_clone = session.clone();
        self.tasks.lock().unwrap().spawn(async move {
            client.handle_session_data(session_clone, stream, rx).await;
        });

        Ok(())
    }

    async fn find_target_peer(&self, _dst_addr: &SocketAddr) -> Result<PeerId> {
        // For now, return the first available peer
        // In a real implementation, you would need routing logic
        let peers = self.peer_manager.list_peers().await;
        if let Some(&peer_id) = peers.first() {
            Ok(peer_id)
        } else {
            Err(anyhow::anyhow!("No available peers"))
        }
    }

    async fn create_proxy_session(&self, session: &ProxySession) -> Result<bool> {
        let _request = ProxyForwardRequest {
            session_id: session.session_id,
            src_addr: Some(session.src_addr.into()),
            dst_addr: Some(session.dst_addr.into()),
            protocol: session.protocol.clone(),
        };

        // Send RPC request to target peer
        // This is a simplified implementation - you would need to use the actual RPC client
        info!("Creating proxy session {} to peer {}", session.session_id, session.peer_id);

        // For now, assume success
        Ok(true)
    }

    async fn handle_session_data(
        &self,
        session: ProxySession,
        stream: TcpStream,
        data_receiver: mpsc::UnboundedReceiver<Vec<u8>>,
    ) {
        let session_id = session.session_id;

        // Split stream for reading and writing
        let (mut read_half, mut write_half) = stream.into_split();

        // Task for reading from local stream and sending to peer
        let client_clone = self.clone();
        let session_clone = session.clone();
        let read_task = tokio::spawn(async move {
            let mut buffer = [0u8; 4096];
            loop {
                match read_half.read(&mut buffer).await {
                    Ok(0) => break, // Connection closed
                    Ok(n) => {
                        let data = buffer[..n].to_vec();
                        if let Err(e) = client_clone.send_data_to_peer(&session_clone, data).await {
                            error!("Failed to send data to peer: {}", e);
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Failed to read from stream: {}", e);
                        break;
                    }
                }
            }
        });

        // Task for receiving data from peer and writing to local stream
        let mut data_receiver = data_receiver;
        let write_task = tokio::spawn(async move {
            while let Some(data) = data_receiver.recv().await {
                if let Err(e) = write_half.write_all(&data).await {
                    error!("Failed to write to stream: {}", e);
                    break;
                }
            }
        });

        // Wait for either task to complete
        tokio::select! {
            _ = read_task => {},
            _ = write_task => {},
        }

        // Clean up session
        self.cleanup_session(session_id).await;
    }

    async fn send_data_to_peer(&self, session: &ProxySession, data: Vec<u8>) -> Result<()> {
        // Send data to peer via RPC
        // This is a simplified implementation
        info!("Sending {} bytes for session {}", data.len(), session.session_id);
        Ok(())
    }

    async fn cleanup_session(&self, session_id: u32) {
        info!("Cleaning up session {}", session_id);
        self.active_sessions.remove(&session_id);
        self.data_senders.remove(&session_id);

        // Send close message to peer
        // This is a simplified implementation
    }
}

impl Clone for ProxyForwardClient {
    fn clone(&self) -> Self {
        Self {
            global_ctx: self.global_ctx.clone(),
            peer_manager: self.peer_manager.clone(),
            config: self.config.clone(),
            session_counter: AtomicU32::new(self.session_counter.load(Ordering::SeqCst)),
            active_sessions: self.active_sessions.clone(),
            tasks: self.tasks.clone(),
            data_senders: self.data_senders.clone(),
        }
    }
}
