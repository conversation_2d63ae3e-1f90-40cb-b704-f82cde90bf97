_version: 2

core_clap:
  config_server:
    en: |+
      config server address, allow format:
      full url: --config-server udp://127.0.0.1:22020/admin
      only user name: --config-server admin, will use official server
    zh-CN: |+
      配置服务器地址。允许格式：
      完整URL：--config-server udp://127.0.0.1:22020/admin
      仅用户名：--config-server admin，将使用官方的服务器
  config_file:
    en: "path to the config file, NOTE: the options set by cmdline args will override options in config file"
    zh-CN: "配置文件路径，注意：命令行中的配置的选项会覆盖配置文件中的选项"
  network_name:
    en: "network name to identify this vpn network"
    zh-CN: "用于标识此VPN网络的网络名称"
  network_secret:
    en: "network secret to verify this node belongs to the vpn network"
    zh-CN: "网络密钥，用于验证此节点属于VPN网络"
  ipv4:
    en: "ipv4 address of this vpn node, if empty, this node will only forward packets and no TUN device will be created"
    zh-CN: "此VPN节点的IPv4地址，如果为空，则此节点将仅转发数据包，不会创建TUN设备"
  dhcp:
    en: "automatically determine and set IP address by Easytier, and the IP address starts from ******** by default. Warning, if there is an IP conflict in the network when using DHCP, the IP will be automatically changed."
    zh-CN: "由Easytier自动确定并设置IP地址，默认从********开始。警告：在使用DHCP时，如果网络中出现IP冲突，IP将自动更改。"
  peers:
    en: "peers to connect initially"
    zh-CN: "最初要连接的对等节点"
  external_node:
    en: "use a public shared node to discover peers"
    zh-CN: "使用公共共享节点来发现对等节点"
  proxy_networks:
    en: "export local networks to other peers in the vpn"
    zh-CN: "将本地网络导出到VPN中的其他对等节点"
  rpc_portal:
    en: "rpc portal address to listen for management. 0 means random port, 12345 means listen on 12345 of localhost, 0.0.0.0:12345 means listen on 12345 of all interfaces. default is 0 and will try 15888 first"
    zh-CN: "用于管理的RPC门户地址。0表示随机端口，12345表示在localhost的12345上监听，0.0.0.0:12345表示在所有接口的12345上监听。默认是0，首先尝试15888"
  listeners:
    en: |+
        listeners to accept connections, allow format:
        port number: <11010>. means tcp/udp will listen on 11010, ws/wss will listen on 11010 and 11011, wg will listen on 11011
        url: <tcp://0.0.0.0:11010>. tcp can be tcp, udp, ring, wg, ws, wss\n
        proto & port pair: <proto:port>. wg:11011, means listen on 11011 with wireguard protocol url and proto:port can occur multiple times.
    zh-CN: |+
      监听器用于接受连接，允许以下格式：
      端口号：<11010>，意味着tcp/udp将在11010端口监听，ws/wss将在11010和11011端口监听，wg将在11011端口监听。
      url：<tcp://0.0.0.0:11010>，其中tcp可以是tcp、udp、ring、wg、ws、wss协议。
      协议和端口对：<proto:port>，例如wg:11011，表示使用WireGuard协议在11011端口监听。URL 和 协议端口对 可以多次出现。
  no_listener:
    en: "do not listen on any port, only connect to peers"
    zh-CN: "不监听任何端口，只连接到对等节点"
  console_log_level:
    en: "console log level"
    zh-CN: "控制台日志级别"
  file_log_level:
    en: "file log level"
    zh-CN: "文件日志级别"
  file_log_dir:
    en: "directory to store log files"
    zh-CN: "存储日志文件的目录"
  hostname:
    en: "host name to identify this device"
    zh-CN: "用于标识此设备的主机名"
  instance_name:
    en: "instance name to identify this vpn node in same machine"
    zh-CN: "实例名称，用于在同一台机器上标识此VPN节点"
  vpn_portal:
    en: "url that defines the vpn portal, allow other vpn clients to connect. example: wg://0.0.0.0:11010/**********/24, means the vpn portal is a wireguard server listening on vpn.example.com:11010, and the vpn client is in network of **********/24"
    zh-CN: "定义VPN门户的URL，允许其他VPN客户端连接。示例：wg://0.0.0.0:11010/**********/24，表示VPN门户是监听在vpn.example.com:11010的wireguard服务器，VPN客户端在**********/24网络中"
  default_protocol:
    en: "default protocol to use when connecting to peers"
    zh-CN: "连接到对等节点时使用的默认协议"
  disable_encryption:
    en: "disable encryption for peers communication, default is false, must be same with peers"
    zh-CN: "禁用对等节点通信的加密，默认为false，必须与对等节点相同"
  multi_thread:
    en: "use multi-thread runtime, default is single-thread"
    zh-CN: "使用多线程运行时，默认为单线程"
  disable_ipv6:
    en: "do not use ipv6"
    zh-CN: "不使用IPv6"
  dev_name:
    en: "optional tun interface name"
    zh-CN: "可选的TUN接口名称"
  mtu:
    en: "mtu of the TUN device, default is 1380 for non-encryption, 1360 for encryption"
    zh-CN: "TUN设备的MTU，默认为非加密时为1380，加密时为1360"
  latency_first:
    en: "latency first mode, will try to relay traffic with lowest latency path, default is using shortest path"
    zh-CN: "延迟优先模式，将尝试使用最低延迟路径转发流量，默认使用最短路径"
  exit_nodes:
    en: "exit nodes to forward all traffic to, a virtual ipv4 address, priority is determined by the order of the list"
    zh-CN: "转发所有流量的出口节点，虚拟IPv4地址，优先级由列表顺序决定"
  enable_exit_node:
    en: "allow this node to be an exit node"
    zh-CN: "允许此节点成为出口节点"
  proxy_forward_by_system:
    en: "forward packet to proxy networks via system kernel, disable internal nat for network proxy"
    zh-CN: "通过系统内核转发子网代理数据包，禁用内置NAT"
  no_tun:
    en: "do not create TUN device, can use subnet proxy to access node"
    zh-CN: "不创建TUN设备，可以使用子网代理访问节点"
  use_smoltcp:
    en: "enable smoltcp stack for subnet proxy and kcp proxy"
    zh-CN: "为子网代理和 KCP 代理启用smoltcp堆栈"
  manual_routes:
    en: "assign routes cidr manually, will disable subnet proxy and wireguard routes propagated from peers. e.g.: ***********/16"
    zh-CN: "手动分配路由CIDR，将禁用子网代理和从对等节点传播的wireguard路由。例如：***********/16"
  relay_network_whitelist:
    en: |+
        only forward traffic from the whitelist networks, supporting wildcard strings, multiple network names can be separated by spaces.
        if local network (assigned with network_name) is not in the whitelist, the traffic can still be forwarded if no other route path is available.
        if this parameter is empty, forwarding is disabled. by default, all networks are allowed.
        e.g.: '*' (all networks), 'def*' (networks with the prefix 'def'), 'net1 net2' (only allow net1 and net2)"
    zh-CN: |+
        仅转发白名单网络的流量，支持通配符字符串。多个网络名称间可以使用英文空格间隔。
        如果本地网络（使用 network_name 分配）不在白名单中，如果没有其他路由路径可用，流量仍然可以转发。
        如果该参数为空，则禁用转发。默认允许所有网络。
        例如：'*'（所有网络），'def*'（以def为前缀的网络），'net1 net2'（只允许net1和net2）"
  disable_p2p:
    en: "disable p2p communication, will only relay packets with peers specified by --peers"
    zh-CN: "禁用P2P通信，只通过--peers指定的节点转发数据包"
  disable_udp_hole_punching:
    en: "disable udp hole punching"
    zh-CN: "禁用UDP打洞功能"
  relay_all_peer_rpc:
    en: "relay all peer rpc packets, even if the peer is not in the relay network whitelist. this can help peers not in relay network whitelist to establish p2p connection."
    zh-CN: "转发所有对等节点的RPC数据包，即使对等节点不在转发网络白名单中。这可以帮助白名单外网络中的对等节点建立P2P连接。"
  socks5:
    en: "enable socks5 server, allow socks5 client to access virtual network. format: <port>, e.g.: 1080"
    zh-CN: "启用 socks5 服务器，允许 socks5 客户端访问虚拟网络. 格式: <端口>，例如：1080"
  ipv6_listener:
    en: "the url of the ipv6 listener, e.g.: tcp://[::]:11010, if not set, will listen on random udp port"
    zh-CN: "IPv6 监听器的URL，例如：tcp://[::]:11010，如果未设置，将在随机UDP端口上监听"
  compression:
    en: "compression algorithm to use, support none, zstd. default is none"
    zh-CN: "要使用的压缩算法，支持 none、zstd。默认为 none"
  mapped_listeners:
    en: "manually specify the public address of the listener, other nodes can use this address to connect to this node. e.g.: tcp://***************:11223, can specify multiple."
    zh-CN: "手动指定监听器的公网地址，其他节点可以使用该地址连接到本节点。例如：tcp://***************:11223，可以指定多个。"
  bind_device:
    en: "bind the connector socket to physical devices to avoid routing issues. e.g.: subnet proxy segment conflicts with a node's segment, after binding the physical device, it can communicate with the node normally."
    zh-CN: "将连接器的套接字绑定到物理设备以避免路由问题。比如子网代理网段与某节点的网段冲突，绑定物理设备后可以与该节点正常通信。"
  enable_kcp_proxy:
    en: "proxy tcp streams with kcp, improving the latency and throughput on the network with udp packet loss."
    zh-CN: "使用 KCP 代理 TCP 流，提高在 UDP 丢包网络上的延迟和吞吐量。"
  disable_kcp_input:
    en: "do not allow other nodes to use kcp to proxy tcp streams to this node. when a node with kcp proxy enabled accesses this node, the original tcp connection is preserved."
    zh-CN: "不允许其他节点使用 KCP 代理 TCP 流到此节点。开启 KCP 代理的节点访问此节点时，依然使用原始 TCP 连接。"
  port_forward:
    en: "forward local port to remote port in virtual network. e.g.: udp://0.0.0.0:12345/************:23456, means forward local udp port 12345 to ************:23456 in the virtual network. can specify multiple."
    zh-CN: "将本地端口转发到虚拟网络中的远程端口。例如：udp://0.0.0.0:12345/************:23456，表示将本地UDP端口12345转发到虚拟网络中的************:23456。可以指定多个。"
  accept_dns:
    en: "if true, enable magic dns. with magic dns, you can access other nodes with a domain name, e.g.: <hostname>.et.net. magic dns will modify your system dns settings, enable it carefully."
    zh-CN: "如果为true，则启用魔法DNS。使用魔法DNS，您可以使用域名访问其他节点，例如：<hostname>.et.net。魔法DNS将修改您的系统DNS设置，请谨慎启用。"

core_app:
  panic_backtrace_save:
    en: "backtrace saved to easytier-panic.log"
    zh-CN: "回溯信息已保存到easytier-panic.log"
