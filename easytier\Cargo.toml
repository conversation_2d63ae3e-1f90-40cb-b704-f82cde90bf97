[package]
name = "easytier"
description = "A full meshed p2p VPN, connecting all your devices in one network with one command."
homepage = "https://github.com/EasyTier/EasyTier"
repository = "https://github.com/EasyTier/EasyTier"
version = "2.3.0"
edition = "2021"
authors = ["kkrainbow"]
keywords = ["vpn", "p2p", "network", "easytier"]
categories = ["network-programming", "command-line-utilities"]
rust-version = "1.84.0"
license-file = "LICENSE"
readme = "README.md"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[[bin]]
name = "easytier-core"
path = "src/easytier-core.rs"
test = false

[[bin]]
name = "easytier-cli"
path = "src/easytier-cli.rs"

[lib]
name = "easytier"
path = "src/lib.rs"

[dependencies]
git-version = "0.3.9"

tracing = { version = "0.1", features = ["log"] }
tracing-subscriber = { version = "0.3", features = [
    "env-filter",
    "local-time",
    "time",
] }
tracing-appender = "0.2.3"
thiserror = "1.0"
auto_impl = "1.1.0"
crossbeam = "0.8.4"
time = "0.3"
toml = "0.8.12"
chrono = { version = "0.4.37", features = ["serde"] }

gethostname = "0.5.0"

futures = { version = "0.3", features = ["bilock", "unstable"] }

tokio = { version = "1", features = ["full"] }
tokio-stream = "0.1"
tokio-util = { version = "0.7.9", features = ["codec", "net", "io"] }

async-stream = "0.3.5"
async-trait = "0.1.74"

dashmap = "6.0"
timedmap = "=1.0.1"

# for full-path zero-copy
zerocopy = { version = "0.7.32", features = ["derive", "simd"] }
bytes = "1.5.0"
pin-project-lite = "0.2.13"
tachyonix = "0.3.0"

quinn = { version = "0.11.0", optional = true, features = ["ring"] }
rustls = { version = "0.23.0", features = [
    "ring",
], default-features = false, optional = true }
rcgen = { version = "0.12.1", optional = true }

# for websocket
tokio-websockets = { version = "0.8", optional = true, features = [
    "rustls-webpki-roots",
    "client",
    "server",
    "fastrand",
    "ring",
] }
http = { version = "1", default-features = false, features = [
    "std",
], optional = true }
tokio-rustls = { version = "0.26", default-features = false, optional = true }

# for tap device
tun = { package = "tun-easytier", version = "1.1.1", features = [
    "async",
], optional = true }
# for net ns
nix = { version = "0.29.0", features = ["sched", "socket", "ioctl", "net"] }

uuid = { version = "1.5.0", features = [
    "v4",
    "fast-rng",
    "macro-diagnostics",
    "serde",
] }

# for ring tunnel
once_cell = "1.18.0"

# for rpc
prost = "0.13"
prost-types = "0.13"
anyhow = "1.0"

url = { version = "2.5", features = ["serde"] }
percent-encoding = "2.3.1"

# for tun packet
byteorder = "1.5.0"

# for proxy
cidr = { version = "0.2.2", features = ["serde"] }
socket2 = "0.5.5"

# for hole punching
stun_codec = "0.3.4"
bytecodec = "0.4.15"
rand = "0.8.5"

serde = { version = "1.0", features = ["derive"] }
pnet = { version = "0.35.0", features = ["serde"] }
serde_json = "1"

clap = { version = "4.5.30", features = [
    "string",
    "unicode",
    "derive",
    "wrap_help",
    "env",
] }

async-recursion = "1.0.5"

network-interface = "2.0"

# for ospf route
petgraph = "0.8.1"

# for wireguard
boringtun = { package = "boringtun-easytier", version = "0.6.1", optional = true }

# for encryption
ring = { version = "0.17", optional = true }
bitflags = "2.5"
aes-gcm = { version = "0.10.3", optional = true }

# for cli
tabled = "0.16"
humansize = "2.1.3"

base64 = "0.22"

mimalloc-rust = { git = "https://github.com/EasyTier/mimalloc-rust", optional = true }

# mips
atomic-shim = "0.2.0"

smoltcp = { version = "0.12.0", optional = true, default-features = false, features = [
    "std",
    "medium-ip",
    "proto-ipv4",
    "proto-ipv6",
    "proto-ipv4-fragmentation",
    "fragmentation-buffer-size-8192",
    "assembler-max-segment-count-16",
    "reassembly-buffer-size-8192",
    "reassembly-buffer-count-16",
    "socket-tcp",
    "socket-udp",
    # "socket-tcp-cubic",
    "async",
] }
parking_lot = { version = "0.12.0", optional = true }

wildmatch = "2.3.4"

rust-i18n = "3"
sys-locale = "0.3"

ringbuf = "0.4.5"
async-ringbuf = "0.3.1"

service-manager = { git = "https://github.com/chipsenkbeil/service-manager-rs.git", branch = "main" }

async-compression = { version = "0.4.17", default-features = false, features = [
    "zstd",
    "tokio",
] }

kcp-sys = { git = "https://github.com/EasyTier/kcp-sys" }

prost-reflect = { version = "0.14.5", default-features = false, features = [
    "derive",
] }

# for http connector
http_req = { git = "https://github.com/EasyTier/http_req.git", default-features = false, features = [
    "rust-tls",
] }

# for dns connector
hickory-resolver = "0.25.2"
hickory-proto = "0.25.2"

# for magic dns
hickory-client = "0.25.2"
hickory-server = { version = "0.25.2", features = ["resolver"] }
derive_builder = "0.20.2"
humantime-serde = "1.1.1"
multimap = "0.10.0"
version-compare = "0.2.0"

bounded_join_set = "0.3.0"

jemallocator = { version = "0.5.4", optional = true }
jemalloc-ctl = { version = "0.5.4", optional = true }
jemalloc-sys = { version = "0.5.4", features = [
    "stats",
    "profiling",
    "unprefixed_malloc_on_supported_platforms",
], optional = true }

[target.'cfg(any(target_os = "linux", target_os = "macos", target_os = "windows", target_os = "freebsd"))'.dependencies]
machine-uid = "0.5.3"

[target.'cfg(any(target_os = "linux"))'.dependencies]
netlink-sys = "0.8.7"
netlink-packet-route = "0.21.0"
netlink-packet-core = { version = "0.7.0" }
netlink-packet-utils = "0.5.2"
# for magic dns
resolv-conf = "0.7.3"
dbus = { version = "0.9.7", features = ["vendored"] }
which = "7.0.3"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.52.0", features = [
    "Win32_Foundation",
    "Win32_NetworkManagement_WindowsFirewall",
    "Win32_System_Com",
    "Win32_Networking",
    "Win32_System_Ole",
    "Win32_Networking_WinSock",
    "Win32_System_IO",
] }
encoding = "0.2"
winreg = "0.52"
windows-service = "0.7.0"

[build-dependencies]
tonic-build = "0.12"
globwalk = "0.8.1"
regex = "1"
prost-build = "0.13.2"
rpc_build = { package = "easytier-rpc-build", version = "0.1.0", features = [
    "internal-namespace",
] }
prost-reflect-build = { version = "0.14.0" }

[target.'cfg(windows)'.build-dependencies]
reqwest = { version = "0.12.12", features = ["blocking"] }
zip = "0.6.6"

# enable thunk-rs when compiling for x86_64 or i686 windows
[target.x86_64-pc-windows-msvc.build-dependencies]
thunk-rs = { git = "https://github.com/easytier/thunk.git", default-features = false, features = ["win7"] }

[target.i686-pc-windows-msvc.build-dependencies]
thunk-rs = { git = "https://github.com/easytier/thunk.git", default-features = false, features = ["win7"] }


[dev-dependencies]
serial_test = "3.0.0"
rstest = "0.18.2"
futures-util = "0.3.30"
maplit = "1.0.2"

[target.'cfg(target_os = "linux")'.dev-dependencies]
defguard_wireguard_rs = "0.4.2"
tokio-socks = "0.5.2"


[features]
default = ["wireguard", "mimalloc", "websocket", "smoltcp", "tun", "socks5"]
full = [
    "quic",
    "websocket",
    "wireguard",
    "mimalloc",
    "aes-gcm",
    "smoltcp",
    "tun",
    "socks5",
]
mips = ["aes-gcm", "mimalloc", "wireguard", "tun", "smoltcp", "socks5"]
wireguard = ["dep:boringtun", "dep:ring"]
quic = ["dep:quinn", "dep:rustls", "dep:rcgen"]
mimalloc = ["dep:mimalloc-rust"]
aes-gcm = ["dep:aes-gcm"]
tun = ["dep:tun"]
websocket = [
    "dep:tokio-websockets",
    "dep:http",
    "dep:tokio-rustls",
    "dep:rustls",
    "dep:rcgen",
]
smoltcp = ["dep:smoltcp", "dep:parking_lot"]
socks5 = ["dep:smoltcp"]
jemalloc = ["dep:jemallocator", "dep:jemalloc-ctl", "dep:jemalloc-sys"]
