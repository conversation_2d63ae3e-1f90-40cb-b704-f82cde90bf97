# EasyTier 代理转发配置示例
# 此配置演示了如何在A设备上配置B设备的端口代理转发功能

# 基本网络配置
instance_name = "proxy_client"
ipv4 = "*************"
network_name = "default"
network_secret = "your_secret_key"

# 代理转发配置
[proxy_forward.environments.server]
# 目标服务器地址（B设备局域网中的服务器）
server = "*************"
# 端口映射：本地端口-远程端口
port = [
    "400-22",      # 本地400端口映射到远程22端口(SSH)
    "401-3306",    # 本地401端口映射到远程3306端口(MySQL)
    "402-80",      # 本地402端口映射到远程80端口(HTTP)
    "403-443"      # 本地403端口映射到远程443端口(HTTPS)
]

[proxy_forward.environments.jumpserver]
# 另一个目标服务器
server = "*************"
port = [
    "500-22",      # 跳板机SSH访问
    "501-8080"     # 跳板机Web管理界面
]

[proxy_forward.environments.database]
# 数据库服务器
server = "*************"
port = [
    "600-5432",    # PostgreSQL
    "601-6379",    # Redis
    "602-27017"    # MongoDB
]

# 其他EasyTier配置
listeners = ["tcp://0.0.0.0:11010", "udp://0.0.0.0:11010"]

# 对等节点配置（B设备的地址）
peers = ["tcp://peer_b_public_ip:11010"]

# 启用无TUN模式（如果需要）
# no_tun = true

# 日志级别
# log_level = "info"
