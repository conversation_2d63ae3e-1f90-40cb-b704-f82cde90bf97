network: 网络
networking_method: 网络方式
public_server: 公共服务器
manual: 手动
standalone: 独立
virtual_ipv4: 虚拟IPv4地址
virtual_ipv4_dhcp: DHCP
network_name: 网络名称
network_secret: 网络密码
public_server_url: 公共服务器地址
peer_urls: 对等节点地址
proxy_cidrs: 子网代理CIDR
enable_vpn_portal: 启用VPN门户
vpn_portal_listen_port: 监听端口
vpn_portal_client_network: 客户端子网
dev_name: TUN接口名称
advanced_settings: 高级设置
basic_settings: 基础设置
listener_urls: 监听地址
rpc_port: RPC端口
config_network: 配置网络
running: 运行中
error_msg: 错误信息
detail: 详情
add_new_network: 添加新网络
del_cur_network: 删除当前网络
select_network: 选择网络
network_instances: 网络实例
instance_id: 实例ID
network_infos: 网络信息
parse_network_config: 解析网络配置
retain_network_instance: 保留网络实例
collect_network_infos: 收集网络信息
settings: 设置
exchange_language: Switch to English
logging: 日志
logging_level_info: 信息
logging_level_debug: 调试
logging_level_warn: 警告
logging_level_trace: 跟踪
logging_level_off: 关闭
logging_open_dir: 打开日志目录
logging_copy_dir: 复制日志路径
disable_auto_launch: 关闭开机自启
enable_auto_launch: 开启开机自启
exit: 退出
chips_placeholder: 例如： {0}, 按回车添加
hostname_placeholder: '留空默认为主机名: {0}'
dev_name_placeholder: 注意：当多个网络同时使用相同的TUN接口名称时，将会在设置TUN的IP时产生冲突，留空以自动生成随机名称
off_text: 点击关闭
on_text: 点击开启
show_config: 显示配置
close: 关闭

use_latency_first: 延迟优先模式
my_node_info: 当前节点信息
peer_count: 已连接
upload: 上传
download: 下载
show_vpn_portal_config: 显示VPN门户配置
vpn_portal_config: VPN门户配置
show_event_log: 显示事件日志
event_log: 事件日志
peer_info: 节点信息
hostname: 主机名
route_cost: 路由
latency: 延迟
upload_bytes: 上传
download_bytes: 下载
loss_rate: 丢包率

status:
  version: 内核版本
  local: 本机
  server: 服务器
  relay: 中继

run_network: 运行网络
stop_network: 停止网络
network_running: 运行中
network_stopped: 已停止
dhcp_experimental_warning: 实验性警告！使用DHCP时如果组网环境中发生IP冲突，将自动更改IP。

tray:
  show: 显示 / 隐藏
  exit: 退出

about:
  title: 关于
  version: 版本
  author: 作者
  homepage: 主页
  license: 许可证
  description: 一个简单、安全、去中心化的内网穿透 VPN 组网方案，使用 Rust 语言和 Tokio 框架实现。
  check_update: 检查更新

event:
  Unknown: 未知
  TunDeviceReady: Tun设备就绪
  TunDeviceError: Tun设备错误
  PeerAdded: 对端添加
  PeerRemoved: 对端移除
  PeerConnAdded: 对端连接添加
  PeerConnRemoved: 对端连接移除
  ListenerAdded: 监听器添加
  ListenerAddFailed: 监听器添加失败
  ListenerAcceptFailed: 监听器接受连接失败
  ConnectionAccepted: 连接已接受
  ConnectionError: 连接错误
  Connecting: 正在连接
  ConnectError: 连接错误
  VpnPortalClientConnected: VPN门户客户端已连接
  VpnPortalClientDisconnected: VPN门户客户端已断开连接
  DhcpIpv4Changed: DHCP IPv4地址更改
  DhcpIpv4Conflicted: DHCP IPv4地址冲突
  PortForwardAdded: 端口转发添加
